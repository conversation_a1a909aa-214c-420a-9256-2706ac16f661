<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent">

    <TextView
        android:id="@id/textThanksMessage"
        style="@style/Description.Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="144dp"
        android:layout_weight="1.0"
        android:text="@string/purchaseRetry"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.47"
        app:layout_constraintStart_toStartOf="parent" />
    <TextView
        android:id="@id/textTransactionInfo"
        style="@style/Description.Bold"
        android:layout_width="900.0px"
        android:layout_height="450.0px"
        android:background="@drawable/rounded_corner"
        android:backgroundTint="@color/Red"
        android:gravity="center"
        android:padding="50.0px"
        android:text="@string/transaction_failed"
        android:textColor="#ffffffff"
        app:cornerRadius="80.0px"
        android:layout_marginTop="80.0dip"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>


    <TextView
        android:id="@id/textProceedtoPump"
        style="@style/Description.Subtitle"
        android:layout_width="800.0px"
        android:layout_height="wrap_content"
        android:layout_marginTop="250dp"
        android:text="@string/pump_instruction"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textTransactionInfo" />

    <TextView
        android:id="@+id/supportNumber"
        android:text="@string/supportDetail"
        android:fontFamily="@font/montserrat_semibold"
        android:textColor="@color/DarkGrey"
        android:textSize="30.0px"
        android:layout_width="550px"
        android:layout_height="150px"
        android:layout_marginLeft="10dp"
        app:flow_horizontalAlign="start"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textThanksMessage"/>

</androidx.constraintlayout.widget.ConstraintLayout>