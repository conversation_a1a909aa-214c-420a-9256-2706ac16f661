package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.blue.components.BlueButton1;
import com.smartfuel.ui.utils.ImageDownloader;

public class BlueRewardCardFragment extends Fragment {

    private final int tintColor;
    private final String rewardText;
    private final boolean enableQr;
    private final String qrUrl;
    private final String cardUrl;

    private ImageDownloader imageDownloader;

    public BlueRewardCardFragment(int tintColor, String rewardText, boolean enableQr, String qrUrl, String cardUrl) {
        this.tintColor = tintColor;
        this.rewardText = rewardText;
        this.enableQr = enableQr;
        this.qrUrl = qrUrl;
        this.cardUrl = cardUrl;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_reward_card, container, false);

        imageDownloader = new ImageDownloader(getContext());

        TextView txtTitle = view.findViewById(R.id.txtTitle);
        txtTitle.setTextColor(tintColor);
        txtTitle.setText(rewardText);

        TextView txtOr = view.findViewById(R.id.txtOr);
        txtOr.setTextColor(tintColor);

        ((CardView)view.findViewById(R.id.scanCard)).setCardBackgroundColor(tintColor);
        ((CardView)view.findViewById(R.id.swipeCard)).setCardBackgroundColor(tintColor);

        if(!qrUrl.isEmpty()) {
            imageDownloader.loadImage(qrUrl, bitmap -> {
                if (bitmap != null) {
                    ((ImageView)view.findViewById(R.id.imgQrCode)).setImageBitmap(bitmap);
                }
            });
        }

        if(!cardUrl.isEmpty()) {
            imageDownloader.loadImage(cardUrl, bitmap -> {
                if (bitmap != null) {
                    ((ImageView)view.findViewById(R.id.imgCard)).setImageBitmap(bitmap);
                }
            });
        }

        ImageView imgLeft = view.findViewById(R.id.imgleft);
        imgLeft.setColorFilter(tintColor);

        ImageView imgRight = view.findViewById(R.id.imgRight);
        imgRight.setColorFilter(tintColor);

        if(!enableQr) {
            imgLeft.setVisibility(View.INVISIBLE);
            txtOr.setVisibility(View.GONE);
            view.findViewById(R.id.layQr).setVisibility(View.GONE);
        }

        return view;
    }
}