<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="#ffffffff"
    android:clickable="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:onClick="onClickScreen">

    <requestFocus />
<ImageView
    android:id="@+id/sf_logo"
    android:layout_width="200dp"
    android:layout_height="200dp"
    android:src="@drawable/sflogo"
    android:paddingTop="10dp"
    android:paddingLeft="20dp"
    app:layout_constraintBottom_toBottomOf="@id/dateTimeBackground"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintStart_toStartOf="parent"/>
    <ImageView
        android:id="@id/dateTimeBackground"
        android:layout_width="510.0px"
        android:layout_height="204.0px"
        android:layout_marginBottom="52.0dip"
        android:contentDescription="@string/ellipse"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ellipse" />

    <TextClock
        android:id="@id/textDateId"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:format12Hour="MMMM dd, yyyy"
        android:gravity="right"
        android:text="@string/date"
        android:textColor="@color/White"
        android:textSize="32.0px"
        app:layout_constraintBottom_toTopOf="@id/infoblock"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.93"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.100000024" />

    <TextClock
        android:id="@id/textClockId"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:format12Hour="hh:mm:ss"
        android:gravity="end"
        android:text="@string/_00_00_00"
        android:textColor="@color/White"
        android:textSize="32.0px"
        app:layout_constraintBottom_toBottomOf="@id/dateTimeBackground"
        app:layout_constraintEnd_toStartOf="@id/textAmPmId"
        app:layout_constraintHorizontal_bias="0.85"
        app:layout_constraintStart_toStartOf="@id/textDateId"
        app:layout_constraintTop_toBottomOf="@id/textDateId"
        app:layout_constraintVertical_bias="0.120000005" />

    <TextClock
        android:id="@id/textAmPmId"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:format12Hour="a"
        android:gravity="end"
        android:text="@string/am"
        android:textColor="@color/White"
        android:textSize="32.0px"
        app:layout_constraintBottom_toBottomOf="@id/dateTimeBackground"
        app:layout_constraintEnd_toEndOf="@id/textDateId"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="@id/textDateId"
        app:layout_constraintTop_toBottomOf="@id/textDateId"
        app:layout_constraintVertical_bias="0.120000005" />
<LinearLayout
    android:id="@+id/infoblock"
    android:layout_width="match_parent"
    android:layout_height="700dp"
    android:orientation="vertical"
    app:layout_constraintTop_toBottomOf="@id/sf_logo"
    app:layout_constraintBottom_toTopOf="@id/ImageTouchIcon"
    android:layout_marginLeft="@dimen/activity_horizontal_margin">
    <TextView
        android:id="@id/TextPayHere1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:fontFamily="@font/montserrat_semibold"
        android:paddingBottom="16sp"
        android:text="@string/instruction1"
        android:textColor="@color/DarkGrey"
        android:textSize="72sp"
        app:layout_constraintBottom_toTopOf="@id/ImageTouchIcon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingTop="16sp"
        android:paddingBottom="16sp"
        android:src="@drawable/icon247"
        android:layout_gravity="center"/>
    <!--<LinearLayout
        android:id="@+id/step1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="150dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:paddingBottom="@dimen/activity_horizontal_margin"
        app:layout_constraintTop_toBottomOf="@id/textClockId">

        <Button
            android:id="@+id/step1Btn"
            android:layout_width="96dp"
            android:layout_height="96dp"
            android:backgroundTint="@color/Red"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:paddingLeft="@dimen/activity_horizontal_margin"
            android:fontFamily="@font/montserrat_semibold"
            android:textSize="36sp"
            android:text="1"
            android:textColor="@color/White"/>

        <TextView
            android:id="@id/TextPayHere1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/montserrat_semibold"
            android:paddingLeft="@dimen/activity_horizontal_margin"
            android:paddingRight="@dimen/activity_horizontal_margin"
            android:text="@string/instruction1"
            android:textColor="@color/DarkGrey"
            android:textSize="72sp"
            app:layout_constraintBottom_toTopOf="@id/ImageTouchIcon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.77" />

    </LinearLayout>
    <LinearLayout
        android:id="@+id/step2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        app:layout_constraintTop_toBottomOf="@id/step1">

        <Button
            android:id="@+id/step2Btn"
            android:layout_width="96dp"
            android:layout_height="96dp"
            android:backgroundTint="@color/Orange"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:paddingLeft="@dimen/activity_horizontal_margin"
            android:fontFamily="@font/montserrat_semibold"
            android:textSize="36sp"
            android:text="2"
            android:textColor="@color/White" />

        <TextView
            android:id="@id/TextPayHere2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/montserrat_semibold"
            android:paddingLeft="@dimen/activity_horizontal_margin"
            android:paddingRight="@dimen/activity_horizontal_margin"
            android:text="@string/instruction2"
            android:textColor="@color/DarkGrey"
            android:textSize="72sp"
            app:layout_constraintBottom_toTopOf="@id/step3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.77" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/step3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:paddingTop="@dimen/activity_horizontal_margin"
        android:paddingBottom="30dp"
        app:layout_constraintTop_toBottomOf="@id/step2">

        <Button
            android:id="@+id/step3Btn"
            android:layout_width="96dp"
            android:layout_height="96dp"
            android:fontFamily="@font/montserrat_semibold"
            android:textSize="36sp"
            android:backgroundTint="@color/Green"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:paddingLeft="@dimen/activity_horizontal_margin"
            android:textColor="@color/White"
            android:text="3" />

        <TextView
            android:id="@id/TextPayHere3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/montserrat_semibold"
            android:paddingLeft="@dimen/activity_horizontal_margin"
            android:paddingRight="@dimen/activity_horizontal_margin"
            android:text="@string/instruction3"
            android:textColor="@color/DarkGrey"
            android:textSize="72sp"
            app:layout_constraintBottom_toTopOf="@id/ImageTouchIcon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.77" />
    </LinearLayout>-->
</LinearLayout>

    <ImageView
        android:id="@id/ImageTouchIcon"
        android:layout_width="250.0px"
        android:layout_height="250.0px"
        android:contentDescription="@string/touch_icon"
        app:layout_constraintBottom_toTopOf="@id/fuelPriceList"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/infoblock"
        app:srcCompat="@drawable/touchicon" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@id/fuelPriceList"
        android:layout_width="1050.0px"
        android:layout_height="350.0px"
        android:layout_marginTop="20.0dip"
        android:layout_marginBottom="20.0dip"
        app:cornerRadius="80.0px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ImageTouchIcon" />
    <!--(02) 8527 0622-->
    <TextView
        android:id="@+id/supportNumber"
        android:text="@string/supportDetail"
        android:fontFamily="@font/montserrat_semibold"
        android:textColor="@color/DarkGrey"
        android:textSize="30.0px"
        android:layout_width="550px"
        android:layout_height="150px"
        android:layout_marginLeft="10dp"
        app:flow_horizontalAlign="start"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/fuelPriceList"/>

    <ImageView
        android:id="@id/receipt"
        android:layout_width="150.0px"
        android:layout_height="150.0px"
        android:layout_marginBottom="50.0dip"
        android:onClick="onReceiptClick"
        android:src="@drawable/receipt_icon"
        app:cornerRadius="80.0px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.878"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/fuelPriceList"/>
</androidx.constraintlayout.widget.ConstraintLayout>