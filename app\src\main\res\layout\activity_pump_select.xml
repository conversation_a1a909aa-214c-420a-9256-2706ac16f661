<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent">

    <!--com.smarteist.autoimageslider.SliderView
        android:id="@id/imageSlider"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/TextPumpSelect"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.65999997"
        app:sliderAnimationDuration="600"
        app:sliderAutoCycleDirection="right"
        app:sliderAutoCycleEnabled="true"
        app:sliderIndicatorAnimationDuration="600"
        app:sliderIndicatorGravity="bottom|center"
        app:sliderIndicatorMargin="15.0dip"
        app:sliderIndicatorOrientation="horizontal"
        app:sliderIndicatorPadding="3.0dip"
        app:sliderIndicatorRadius="2.0dip"
        app:sliderIndicatorSelectedColor="#ff5a5a5a"
        app:sliderIndicatorUnselectedColor="#ffffffff"
        app:sliderScrollTimeInSec="1"
        app:sliderStartAutoCycle="true" /-->

    <ImageView
        android:id="@id/imageSlider"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/TextPumpSelect"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.65999997"
        android:src="@drawable/sf_banner"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"/>

    <TextView
        android:id="@id/TextPumpSelect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20.0dip"
        android:layout_marginBottom="20.0dip"
        android:fontFamily="@font/montserrat"
        android:gravity="center"
        android:text="@string/select_your_pump"
        android:textSize="48.0px"
        app:layout_constraintBottom_toTopOf="@id/FuelPoints"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/imageSlider" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@id/FuelPoints"
        android:layout_width="1050.0px"
        android:layout_height="1400.0px"
        android:layout_marginBottom="40.0dip"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/TextPumpSelect" />
</androidx.constraintlayout.widget.ConstraintLayout>