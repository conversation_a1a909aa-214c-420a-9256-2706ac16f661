<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal">

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_bolditalic"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title1"
        android:textColor="@color/Black"
        android:text="@string/receipt"
        android:layout_marginBottom="@dimen/margin_bottom1"/>

    <TextView
        android:id="@+id/txtInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_regular"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title3"
        android:textColor="@color/blue_black_text"
        android:text="@string/receipt_info2"
        android:layout_marginBottom="@dimen/margin_bottom1"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin1"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin2">

        <com.smartfuel.ui.blue.components.BlueButton1
            android:id="@+id/btnPrint"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="Print receipt"
            android:padding="@dimen/padding_button5"
            android:textSize="@dimen/text_size1"
            android:layout_marginRight="@dimen/activity_horizontal_half_margin2"/>

        <com.smartfuel.ui.blue.components.BlueButton1
            android:id="@+id/btnEmail"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="Email receipt"
            android:padding="@dimen/padding_button5"
            android:textSize="@dimen/text_size1"
            android:layout_marginLeft="@dimen/activity_horizontal_half_margin2"/>

    </LinearLayout>

</LinearLayout>