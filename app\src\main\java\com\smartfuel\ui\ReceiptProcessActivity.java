package com.smartfuel.ui;

import android.app.AlertDialog;
import android.hardware.usb.UsbDevice;
import android.os.Bundle;

import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.hardware.usb.UsbManager;
import android.os.Handler;
import android.os.IBinder;

import com.smartfuel.R;
import com.smartfuel.ReceiptsManager;
import com.smartfuel.service.connection.usb.UsbConnection;
import com.smartfuel.service.connection.usb.UsbPrinterConnection;
import com.smartfuel.service.escposprinter.EscPosPrinter;
import com.smartfuel.service.models.kiosk.response.UserResponse;
import com.smartfuel.service.models.transaction.CardReceipt;
import com.smartfuel.service.models.transaction.WhiteCardReceipt;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.OPTService;


import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class ReceiptProcessActivity extends BaseActivity implements IServiceEvents {
    private static final String ACTION_USB_PERMISSION = "com.smartfuel.USB_PERMISSION";

    protected String cardType;

    @Override
    public void onServiceConnected(ComponentName componentName, IBinder binder) {
        super.onServiceConnected(componentName, binder);
        try {
            getService().registerClient(this);
            cardType = getIntent().getStringExtra("CardType");
            switch (cardType) {
                case "WHITECARD":
                    getService().PrepareCustomerReceipt(getIntent().getStringExtra("QRCodeData"), cardType);
                    try {
                        getService().cancelCardTransaction();
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                    break;
                case "CARD":
                    Intent i = getIntent();
                    Object[] trnNumbers = (Object[]) i.getExtras().get("TransactionNumberDetail");
                    Integer[] trnNumberarray = Arrays.copyOf(trnNumbers, trnNumbers.length, Integer[].class);
                    List<Integer> transactionNumberList = Arrays.stream(trnNumberarray).collect(Collectors.toList());
                    getService().PrepareCustomerReceipt(transactionNumberList, cardType);
                    break;
            }
        } catch (Exception e) {
            systemError("Service Unavailable", e);
        }

    }
   /* private ServiceConnection mConnection = new ServiceConnection() {
        public void onServiceConnected(ComponentName componentName, IBinder binder) {
            try {
                ReceiptProcessActivity.super.onServiceConnected();
                OPTService.LocalBinder localBinder = (OPTService.LocalBinder) binder;
                ReceiptProcessActivity.this.myOPTService = localBinder.getServiceInstance();
                ReceiptProcessActivity.this.myOPTService.registerClient(ReceiptProcessActivity.this);
                cardType = getIntent().getStringExtra("CardType");
                switch (cardType) {
                    case "WHITECARD":
                        myOPTService.PrepareCustomerReceipt(getIntent().getStringExtra("QRCodeData"),cardType);
                        break;
                    case "CARD":
                        myOPTService.PrepareCustomerReceipt(getIntent().getStringExtra("TransactionNumberDetail"),cardType);
                        break;
                }
            } catch (NullPointerException nullPointerException) {
                systemError("No Receipt Data", nullPointerException);
            }


        }

        public void onServiceDisconnected(ComponentName componentName) {
        }
    };*/

    // protected OPTService myOPTService;

    private List<String> receipts;


    private void printUsb() {
        try {

            UsbConnection usbConnection = UsbPrinterConnection.selectElementUSBPrinter(this);
            UsbManager usbManager = (UsbManager) this.getSystemService(Context.USB_SERVICE);

            if (usbConnection == null || usbManager == null) {
                new AlertDialog.Builder(this)
                        .setTitle("Printer Error")
                        .setMessage("Unable to print receipt")
                        .show();
                return;
            }


            PendingIntent permissionIntent = PendingIntent.getBroadcast(
                    this.getApplicationContext(),
                    0,
                    new Intent(this.ACTION_USB_PERMISSION),
                    android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S ? PendingIntent.FLAG_MUTABLE : 0
            );

            usbManager.requestPermission(usbConnection.getDevice(), permissionIntent);

            EscPosPrinter printer = new EscPosPrinter(new UsbConnection(usbManager, usbConnection.getDevice()), 203, 80f, 45);
            for (String data : receipts) {
                printer.printFormattedTextAndCut(data, 500f);
            }
        } catch (Exception e) {
            systemError("Printer Error", e);
           /* setContentView(R.layout.activity_receipt_process);
            TextView t = this.findViewById(R.id.receiptTextView);
            StringWriter sw = new StringWriter();
            e.printStackTrace(new PrintWriter(sw));
            String exceptionAsString = sw.toString();
            t.setText(exceptionAsString);*/
        }


    }

    public void customerReceiptDataReady(List<WhiteCardReceipt> whiteCardReceiptList) {
        this.receipts = ReceiptsManager.getReceiptData(whiteCardReceiptList);
        if (this.receipts.size() > 0)
            printUsb();
        (new Handler()).postDelayed(new Runnable() {
            public void run() {
                Intent intent = new Intent((Context) ReceiptProcessActivity.this, HomeActivity.class);
                ReceiptProcessActivity.this.startActivity(intent);
            }
        }, 15000L);
    }

    public void customerCardReceiptDataReady(List<CardReceipt> cardReceipts) {
        this.receipts = ReceiptsManager.getCardReceiptData(cardReceipts);
        if (this.receipts.size() > 0)
            printUsb();
        (new Handler()).postDelayed(new Runnable() {
            public void run() {
                Intent intent = new Intent((Context) ReceiptProcessActivity.this, HomeActivity.class);
                ReceiptProcessActivity.this.startActivity(intent);
            }
        }, 15000L);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        //unregisterReceiver(this.usbReceiver);
    }

    protected void onCreate(Bundle paramBundle) {
        /*super.onCreate(paramBundle);
        bindService(new Intent((Context) this, OPTService.class), this.mConnection, BIND_AUTO_CREATE);
        setContentView(R.layout.activity_present_payment);*/
        super.onCreate(paramBundle);

        connectToService(this);

    }

    public void serviceReady() {
    }

    public void systemError(String paramString, Throwable paramThrowable) {
        StringWriter stringWriter = new StringWriter();
        if (paramThrowable != null) {
            paramThrowable.printStackTrace(new PrintWriter(stringWriter));
        }
        Intent intent = new Intent((Context) this, SystemErrorActivity.class);
        intent.putExtra("ErrorMessage", paramString);
        intent.putExtra("ErrorStackTrace", stringWriter.toString());
        startActivity(intent);
        finish();
    }

    public void whiteCardUserRejected(UserResponse paramUserResponseResponse) {
    }

    @Override
    public void cardTransactionDeclined(String bankResponse, String cardMask) {

    }

    @Override
    public void cardTransactionInProgress(String pumpNo, long processAmount) {

    }
}

