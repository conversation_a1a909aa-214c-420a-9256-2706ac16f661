package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.smartfuel.R;
import com.smartfuel.service.models.forecourt.FuelPoint;
import com.smartfuel.ui.blue.adapters.BlueFuelPointViewAdapter;

import java.util.ArrayList;

public class BluePumpSelectFragment extends Fragment implements BlueFuelPointViewAdapter.ItemClickListener {

    private final int tintColor;
    private ArrayList<FuelPoint> fuelPoints;
    private BlueFuelPointViewAdapter fuelPointViewAdapter;

    BlueFuelPointViewAdapter.ItemClickListener listener;

    public BluePumpSelectFragment(int tintColor, ArrayList<FuelPoint> fuelPoints) {
        this.tintColor = tintColor;
        this.fuelPoints = fuelPoints;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_pump_select, container, false);

        ((TextView)view.findViewById(R.id.txtSelectPump)).setTextColor(tintColor);

        RecyclerView recyclerView = view.findViewById(R.id.FuelPoints);
        recyclerView.setLayoutManager(
                new GridLayoutManager(
                        getContext(),
                        fuelPoints.size() >= 4 ? 4 : fuelPoints.size())
        );

        fuelPointViewAdapter =
                new BlueFuelPointViewAdapter(
                        getContext(),
                        fuelPoints,
                        tintColor);
        fuelPointViewAdapter.setClickListener(this);

        recyclerView.setAdapter(fuelPointViewAdapter);

        return view;
    }

    @Override
    public void onItemClick(View param1View, int param1Int) {
        if(this.listener != null) {
            listener.onItemClick(param1View, param1Int);
        }
    }

    public void setOnItemClickListener(BlueFuelPointViewAdapter.ItemClickListener listener){
        this.listener = listener;
    }

    public void updateFuelPoints(ArrayList<FuelPoint> fuelPoints){
        this.fuelPoints = fuelPoints;
        fuelPointViewAdapter.updateData(fuelPoints);
    }
}