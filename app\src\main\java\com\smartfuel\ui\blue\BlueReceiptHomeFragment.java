package com.smartfuel.ui.blue;

import android.graphics.Typeface;
import android.os.Bundle;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.blue.components.BlueButton1;

public class BlueReceiptHomeFragment extends Fragment {

    private final int tintColor;
    private final OnClickListener listener;

    public BlueReceiptHomeFragment(int tintColor, OnClickListener listener) {
        this.tintColor = tintColor;
        this.listener = listener;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_receipt_home, container, false);

        ((TextView)view.findViewById(R.id.txtTitle)).setTextColor(tintColor);

        Typeface typeface = ResourcesCompat.getFont(getContext(), R.font.gibson_regular);

        BlueButton1 btnPrint = view.findViewById(R.id.btnPrint);
        btnPrint.setBackgroundColor(tintColor);
        btnPrint.setTypeface(typeface);
        btnPrint.setText(Html.fromHtml("<b>Print receipt</b><br> Receipt will print below.", Html.FROM_HTML_MODE_LEGACY));
        btnPrint.setOnClickListener(v ->{
            listener.onPrintClick();
        });

        BlueButton1 btnEmail = view.findViewById(R.id.btnEmail);
        btnEmail.setBackgroundColor(tintColor);
        btnEmail.setTypeface(typeface);
        btnEmail.setText(Html.fromHtml("<b>Email receipt</b><br> We will send the receipt to you.", Html.FROM_HTML_MODE_LEGACY));
        btnEmail.setOnClickListener(v ->{
            listener.onEmailClick();
        });

        return view;
    }

    public interface OnClickListener{
        void onPrintClick();
        void onEmailClick();
    }
}