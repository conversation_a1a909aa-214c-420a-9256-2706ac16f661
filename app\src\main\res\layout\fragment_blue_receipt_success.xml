<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_bolditalic"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title1"
        android:textColor="@color/Black"
        android:text="@string/receipt"
        android:layout_marginBottom="@dimen/margin_bottom1"/>


    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/card_radius"
        app:cardElevation="0dp"
        app:contentPadding="@dimen/padding_button5"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin4"
        app:cardBackgroundColor="@color/blue_green">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/gibson_bold"
                android:gravity="center_horizontal"
                android:textSize="@dimen/text_title2"
                android:textColor="@color/White"
                android:text="@string/success"
                android:layout_marginBottom="@dimen/margin_bottom3"/>
            <TextView
                android:id="@+id/txtMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/gibson_regular"
                android:gravity="center_horizontal"
                android:textSize="@dimen/text_title2"
                android:textColor="@color/White"
                android:text="@string/success"/>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"/>

    <com.smartfuel.ui.blue.components.BlueButton1
        android:id="@+id/btnClose"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/close"
        android:textSize="@dimen/text_title2"
        android:paddingVertical="@dimen/padding_button2"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin1"
        android:layout_marginBottom="@dimen/margin_bottom1"/>

</LinearLayout>