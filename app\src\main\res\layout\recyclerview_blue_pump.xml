<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/activity_horizontal_half_margin2"
    app:cardCornerRadius="@dimen/card_radius"
    app:cardElevation="0dp">

    <LinearLayout
        android:id="@+id/layBackground"
        android:layout_width="match_parent"
        android:minWidth="@dimen/min_width"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:background="@color/Black"
        android:padding="@dimen/activity_horizontal_half_margin2">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/activity_vertical_margin1"
            android:layout_marginBottom="@dimen/activity_horizontal_half_margin2"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="@dimen/image_size1"
                android:layout_height="@dimen/image_size1"
                android:adjustViewBounds="true"
                android:layout_marginLeft="@dimen/activity_horizontal_half_margin2"
                android:src="@drawable/blue_pump"/>

            <TextView
                android:id="@+id/txtPumpNum"
                android:fontFamily="@font/gibson_bolditalic"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textSize="@dimen/text_size4"
                android:text="1"
                android:textColor="@color/White"/>
        </LinearLayout>

        <TextView
            android:id="@+id/txtInfo"
            android:fontFamily="@font/gibson_bolditalic"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingVertical="@dimen/padding_button7"
            android:textSize="@dimen/text_size1"
            android:text="Select"
            android:textColor="@color/White"/>
    </LinearLayout>

</androidx.cardview.widget.CardView>