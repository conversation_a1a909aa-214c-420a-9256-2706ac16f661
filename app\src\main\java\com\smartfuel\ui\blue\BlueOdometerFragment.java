package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.blue.components.BlueButton1;

public class BlueOdometerFragment extends Fragment {

    private final int tintColor;
    private OnNextCLickListener listener;
    private BlueButton1 btnNext;
    private boolean request;

    public BlueOdometerFragment(int tintColor, OnNextCLickListener listener) {
        this.tintColor = tintColor;
        this.listener = listener;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_odometer, container, false);

        ((TextView)view.findViewById(R.id.txtTitle)).setTextColor(tintColor);
        BlueNumKeyboardFragment fragment = (BlueNumKeyboardFragment)getChildFragmentManager()
                .findFragmentById(R.id.fragment_num_keyboard);
        fragment.setTintColor(tintColor);
        fragment.setMoney(false);
        fragment.setMaxValue(999999);
        fragment.setOnChangeValueListener(value -> {
            changeNextButton(value != 0);
        });

        btnNext = view.findViewById(R.id.btnNext);
        btnNext.setBackgroundColor(tintColor);
        btnNext.setOnClickListener(v -> {
            if(listener != null){
                listener.onNextClick(fragment.getValue());
            }
        });

        changeNextButton(fragment.getValue() != 0);

        return view;
    }

    private void changeNextButton(boolean next){
        if(next || request)
            btnNext.setText(R.string.next);
        else
            btnNext.setText(R.string.skip);
    }

    public void setRequest(boolean request){
        this.request = request;
    }

    public interface OnNextCLickListener {
        void onNextClick(int value);
    }
}