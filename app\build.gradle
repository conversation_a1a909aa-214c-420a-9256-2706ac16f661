plugins {
    id 'com.android.application'
    id 'com.google.gms.google-services'
    id("com.google.firebase.crashlytics")
}

android {
    namespace 'com.smartfuel'
    compileSdk 33
    packagingOptions {
        resources {
            exclude 'META-INF/NOTICE.md'
            exclude 'META-INF/LICENSE.md'
        }
    }
    defaultConfig {
        applicationId 'com.smartfuel'
        minSdk 25
        targetSdk 32
        versionCode 47
        versionName "1.6.3"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        javaCompileOptions {
            annotationProcessorOptions {
                arguments += ["room.schemaLocation": "$projectDir/schemas".toString()]
            }
        }
    }
    packagingOptions {
        resources {
            exclude 'META-INF/NOTICE.md'
            exclude 'META-INF/LICENSE.md'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            signingConfig signingConfigs.debug
            //proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            applicationVariants.all {
                    // this method is use to rename your all apk weather
                    // it may be signed or unsigned(debug apk)
                variant ->
                    variant.outputs.each {
                            // on below line we are setting a
                            // name to our apk as GFG.apk
                        output ->
                            def name = "${versionName}/Smartfuel_kiosk_opt.apk"
                            // on below line we are setting the
                            // outputFile Name to our apk file.
                            output.outputFileName = name
                    }
            }
        }
        debug {
            applicationIdSuffix ".debug"
            debuggable true
        }


    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    testOptions{
        unitTests.returnDefaultValues = true
    }
    buildFeatures {
        viewBinding true
    }
}

dependencies {
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation "androidx.recyclerview:recyclerview:1.3.0"
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'commons-io:commons-io:2.11.0' // DON'T UPGRADE
    implementation 'commons-validator:commons-validator:1.7'
    implementation group: 'org.apache.commons', name: 'commons-lang3', version: '3.12.0'
    implementation platform('com.google.firebase:firebase-bom:32.2.2')
    implementation("com.google.firebase:firebase-crashlytics")
    implementation("com.google.firebase:firebase-analytics")
    implementation 'com.google.android.gms:play-services-location:21.0.1'

    implementation files('libs/smdt.jar')

    //JSON Libs
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.12.7.1'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.12.7'
    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.12.7'

    //Dependency Injection
    implementation 'com.google.dagger:dagger:2.45'
    implementation 'com.google.dagger:dagger-android:2.45'

    annotationProcessor 'com.google.dagger:dagger-android-processor:2.45'
    annotationProcessor 'com.google.dagger:dagger-compiler:2.45'

    //Printer Support Libs
    implementation 'com.google.zxing:core:3.5.1'

    //SQLite Room Database
    implementation "androidx.room:room-runtime:2.5.1"
    annotationProcessor "androidx.room:room-compiler:2.5.1"

    //REST API
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'

    //SMTP Library
    implementation 'com.sun.mail:android-mail:1.5.5'
    implementation 'com.sun.mail:android-activation:1.5.5'
    // SHARED PROJECT REFERENCE
    implementation project(':SmartFuelService')

}