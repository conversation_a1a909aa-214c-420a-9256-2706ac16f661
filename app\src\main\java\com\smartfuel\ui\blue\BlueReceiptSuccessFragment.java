package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.blue.components.BlueButton1;

public class BlueReceiptSuccessFragment extends Fragment {

    private final int tintColor;

    private boolean isPrint;

    private final OnSuccessListener listener;


    public BlueReceiptSuccessFragment(int tintColor, OnSuccessListener listener) {
        this.tintColor = tintColor;
        this.listener = listener;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_receipt_success, container, false);


        TextView title = view.findViewById(R.id.txtTitle);
        title.setTextColor(tintColor);
        if(isPrint)
            title.setText("Print receipt");
        else
            title.setText("Email receipt");

        BlueButton1 btnClose = view.findViewById(R.id.btnClose);
        btnClose.setBackgroundColor(tintColor);
        btnClose.setOnClickListener(v -> {
            listener.onCloseClick();
        });

        if(isPrint)
            ((TextView)view.findViewById(R.id.txtMessage)).setText("Your receipt is printing below.");
        else
            ((TextView)view.findViewById(R.id.txtMessage)).setText("Your receipt has been emailed to you.");

        return view;
    }

    public void setIsPrint(boolean isPrint){
        this.isPrint = isPrint;
    }

    public interface OnSuccessListener{
        void onCloseClick();
    }
}