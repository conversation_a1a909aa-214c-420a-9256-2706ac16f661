package com.smartfuel.ui.blue;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.view.KeyEvent;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.smartfuel.R;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.logger.Log;
import com.smartfuel.service.models.forecourt.FuelPoint;
import com.smartfuel.service.models.kiosk.response.AccountCardCapabilities;
import com.smartfuel.service.sqlite.models.RewardCardDiscount;
import com.smartfuel.ui.BaseActivity;
import com.smartfuel.ui.TransactionProcessActivity;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

public class BlueTransactionActivity extends BaseActivity implements IServiceEvents {

    private final String TAG = this.getClass().getSimpleName();

    BlueAppBarFragment appBarFragment;
    BlueStepIndicatorFragment stepsFragment;
    BluePumpSelectFragment pumpSelectFragment;
    BlueAmountFragment amountFragment;
    BluePreAuthFragment preAuthFragment;
    BlueTransactionProcessFragment transactionProcessFragment;
    BlueTransactionDeclinedFragment transactionDeclinedFragment;
    BlueVehicleFragment vehicleFragment;
    BlueOdometerFragment odometerFragment;
    BlueTransactionSuccessFragment transactionSuccessFragment;
    BlueRewardCardPromptFragment rewardCardPromptFragment;
    BlueRewardCardFragment rewardCardFragment;
    BlueRewardDeclinedFragment rewardDeclinedFragment;
    BlueErrorMessageFragment errorMessageFragment;

    long timeOut = 15000L;
    long transactionTimeOut = 65000L;

    private ArrayList<FuelPoint> fuelPoints;
    private FuelPoint pumpSelected = null;
    private int amountSelected = 0;
    private String rewardText;

    protected long externalReference;
    protected String im30Reference;
    protected AccountCardCapabilities capabilities;

    protected String tintColor = "#1A588E";
    protected String logoUrl = "";
    protected String contact = "";

    protected boolean optQRCodeEnabled = false;
    private static final int BARCODE_TIMEOUT = 500;
    private Handler barcodeHandler = new Handler(Looper.getMainLooper());
    private StringBuilder barcodeBuffer = new StringBuilder();
    private Runnable barcodeRunnable = new Runnable() {
        @Override
        public void run() {
            startTimer(transactionTimeOut);
            //barcodeHandler.removeCallbacks(barcodeRunnable);
            getService().validateRewardCard(externalReference, barcodeBuffer.toString());
            barcodeBuffer.setLength(0);
        }
    };

    protected List<String> steps = new ArrayList<>(Arrays.asList(
        "Select pump",
        "Enter $ amount",
        "Pre-auth payment",
        "Fuel up at selected pump",
        "Return here for a receipt"
    ));

    private void setupTimeOut(){
        setupTimer(timeOut, () -> {
            cancelTransaction(false);
        });
    }

    @Override
    public void onServiceConnected(ComponentName componentName, IBinder binder) {
        super.onServiceConnected(componentName, binder);
        try{
            getService().registerClient(this);
            initialize();
        }catch (Exception e){
            Log.e(TAG, e);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        connectToService(this);
    }

    private void initialize(){
        setContentView(R.layout.activity_blue_transaction);

        tintColor = getService().getConfiguration("tint_color", "#1A588E");
        int tintColorInt = Color.parseColor(tintColor);

        logoUrl = getService().getConfiguration("logo_url", "");

        appBarFragment = (BlueAppBarFragment) getSupportFragmentManager()
                .findFragmentById(R.id.fragment_custom_app_bar);

        if (appBarFragment != null) {
            appBarFragment.setBackClickListener(v -> {
                backTransaction();
            });

            appBarFragment.setCancelClickListener(v -> {
                cancelTransaction(true);
            });

            appBarFragment.setDoneClickListener(v -> {
                cancelTransaction(true);
            });

            appBarFragment.setTintColor(tintColor);

            appBarFragment.setLogo(logoUrl);
        }

        stepsFragment = new BlueStepIndicatorFragment(steps, tintColorInt);
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        transaction.replace(R.id.fragment_steps, stepsFragment);

        fuelPoints = getService().getConfiguredFuelPoints();
        pumpSelectFragment = new BluePumpSelectFragment(tintColorInt, fuelPoints);
        pumpSelectFragment.setOnItemClickListener((param1View, param1Int) -> {
            pumpSelected = fuelPoints.get(param1Int);
            moveTo(amountFragment, true, true, timeOut);
        });
        transaction.replace(R.id.fragment_container, pumpSelectFragment);
        transaction.commit();

        int maxMoney = Integer.parseInt(getService().getConfiguration("max_amount", "999999"));
        boolean showQuickFuel = !getService().getConfiguration("show_quickfuel", "hide").equals("hide");
        int amountQuickFuel = Integer.parseInt(getService().getConfiguration("quickfuel_amount", "500"));
        amountFragment = new BlueAmountFragment(tintColorInt, maxMoney, showQuickFuel, amountQuickFuel, amount ->{
            amountSelected = amount;
            if(amountSelected < 10){
                showMessageError("Minimum amount is $10.00");
                return;
            }
            preAuthFragment.setAmount(amountSelected);
            moveTo(preAuthFragment, true, true, transactionTimeOut);
        });

        boolean showAmex = !getService().getConfiguration("show_amex", "hide").equals("hide");
        boolean showEftpos = !getService().getConfiguration("show_eftpos", "hide").equals("hide");
        boolean showWhiteCard = !getService().getConfiguration("show_whitecard", "hide").equals("hide");
        boolean showRewardCard = !getService().getConfiguration("rewardcard_status", "disabled").equals("disabled");
        rewardText = getService().getConfiguration("reward_text", "Reward Card");
        String whiteCardURL = getService().getConfiguration("whitecard_url", "");
        preAuthFragment = new BluePreAuthFragment(tintColorInt, showAmex, showEftpos, showWhiteCard, showRewardCard, rewardText, whiteCardURL);

        transactionProcessFragment = new BlueTransactionProcessFragment(tintColorInt, logoUrl);

        contact = getService().getConfiguration("contact_text", "");
        transactionDeclinedFragment = new BlueTransactionDeclinedFragment(tintColorInt, logoUrl, contact, new BlueTransactionDeclinedFragment.DeclinedListener() {
            @Override
            public void onTimeOut() {
                runOnUiThread(() -> {
                    moveTo(preAuthFragment, false, false, transactionTimeOut);
                });
            }

            @Override
            public void onCancelClick() {
                runOnUiThread(() ->{
                    cancelTransaction(true);
                });
            }
        });

        vehicleFragment = new BlueVehicleFragment(tintColorInt, value -> {
            runOnUiThread(() -> {
                capabilities.setInputVehicleRegistration(value);
                if(!capabilities.validateVehicle()) {
                    showMessageError(capabilities.getErrorMessage());
                    return;
                }
                if(capabilities != null && capabilities.isPromptOdometer())
                    moveTo(odometerFragment, true, true, transactionTimeOut);
                else {
                    getService().validateTransaction(externalReference, im30Reference, capabilities);
                    moveTo(transactionProcessFragment, true, false, transactionTimeOut);
                }

            });
        });

        odometerFragment = new BlueOdometerFragment(tintColorInt, value -> {
            runOnUiThread(() -> {
                capabilities.setInputOdometer(value != 0 ? String.valueOf(value) : "");
                if (!capabilities.validateOdometer())
                    showMessageError(capabilities.getErrorMessage());
                else {
                    getService().validateTransaction(externalReference, im30Reference, capabilities);
                    moveTo(transactionProcessFragment, true, false, transactionTimeOut);
                }
            });
        });

        String gradeColors = getService().getConfiguration("grade_colors", "");
        transactionSuccessFragment = new BlueTransactionSuccessFragment(tintColorInt, getService().getConfiguredGradePrices().getGradePrices(), gradeColors);

        boolean showQr = !getService().getConfiguration("show_qr", "hide").equals("hide");
        String qrUrl = getService().getConfiguration("qr_url", "");
        String rewardUrl = getService().getConfiguration("reward_url", "");
        rewardCardPromptFragment = new BlueRewardCardPromptFragment(tintColorInt, rewardText, showQr, qrUrl, rewardUrl, new BlueRewardCardPromptFragment.OnClickListener() {
            @Override
            public void onClickYes() {
                runOnUiThread(() ->{
                    moveTo(rewardCardFragment, true, false, transactionTimeOut);
                });
            }

            @Override
            public void onClickNo() {
                proceedPrepaidTransaction();
            }
        });

        rewardCardFragment = new BlueRewardCardFragment(tintColorInt, rewardText, showQr, qrUrl, rewardUrl);

        rewardDeclinedFragment = new BlueRewardDeclinedFragment(tintColorInt, rewardText, showQr, qrUrl, rewardUrl, new BlueRewardDeclinedFragment.DeclinedListener() {
            @Override
            public void onTimeOut(boolean repeat) {
                if(repeat) {
                    runOnUiThread(() -> {
                        moveTo(rewardCardFragment, false, false, transactionTimeOut);
                    });
                }
                else
                    proceedPrepaidTransaction();
            }

            @Override
            public void onCancelClick() {
                proceedPrepaidTransaction();
            }
        });

        errorMessageFragment = new BlueErrorMessageFragment(tintColorInt, logoUrl, contact, new BlueErrorMessageFragment.OnErrorMessageListener() {
            @Override
            public void onTimeOut(Fragment currentFragment) {
                runOnUiThread(() -> {
                    moveTo(currentFragment, false, false, transactionTimeOut);
                });
            }

            @Override
            public void onBackClick(Fragment currentFragment) {
                runOnUiThread(() ->{
                    moveTo(currentFragment, false, false, transactionTimeOut);
                });
            }
        });

        setupTimeOut();
    }

    private void proceedPrepaidTransaction(){
        runOnUiThread(()-> {
            moveTo(transactionProcessFragment, true, false, transactionTimeOut);
            new Thread(() ->{
                try {
                    getService().ProceedPrepaidTransaction(externalReference);
                } catch (InterruptedException e) {
                    Log.e(TAG, e);
                }
            }).start();
        });
    }

    private String getPumpNo(){
        if(pumpSelected != null)
            return String.valueOf(Integer.parseInt((pumpSelected.getId())));
        else return "0";
    }

    private void moveTo(Fragment fragment, boolean forward, boolean moveStep, long timeOut){
        startTimer(timeOut);
        FragmentManager fragmentManager = getSupportFragmentManager();

        Fragment currentFragment = fragmentManager.findFragmentById(R.id.fragment_container);

        try {
            if(fragment == preAuthFragment){
                getService().initialiseTransaction(getPumpNo(), amountSelected * 100);
            }
            else if (fragment == rewardCardFragment){
                barcodeBuffer.setLength(0);
                this.optQRCodeEnabled = true;
                appBarFragment.showCancel(true);
                getService().initialiseMagnetic();
            }
            if(fragment == rewardCardPromptFragment || fragment == rewardDeclinedFragment || fragment == errorMessageFragment
                    || fragment == vehicleFragment || (fragment == odometerFragment && capabilities != null && !capabilities.isPromptVehicle()))
                appBarFragment.showBack(false);
            else
                appBarFragment.showBack(true);
            if(fragment == transactionSuccessFragment){
                appBarFragment.showBack(false);
                appBarFragment.showCancel(false);
                appBarFragment.showDone(true);
            }

            if (fragment == transactionProcessFragment || fragment == transactionDeclinedFragment){
                appBarFragment.getView().setVisibility(View.GONE);
                stepsFragment.getView().setVisibility(View.GONE);
            }
            else{
                appBarFragment.getView().setVisibility(View.VISIBLE);
                stepsFragment.getView().setVisibility(View.VISIBLE);
            }
            if (currentFragment != null) {
                if (currentFragment == preAuthFragment && fragment != transactionProcessFragment) {
                    getService().cancelCardTransaction(getPumpNo());
                }
                if (currentFragment == rewardCardFragment) {
                    this.optQRCodeEnabled = false;
                    getService().cancelCardTransaction();
                }
                if(currentFragment == transactionDeclinedFragment && fragment == transactionDeclinedFragment){
                    return;
                }
                if(currentFragment == rewardDeclinedFragment && fragment == rewardDeclinedFragment){
                    return;
                }
                if(currentFragment == errorMessageFragment && fragment == errorMessageFragment){
                    return;
                }
                if(fragment == errorMessageFragment)
                    errorMessageFragment.setCurrentFragment(currentFragment);
            }

            FragmentTransaction transaction = fragmentManager.beginTransaction();
            int animEnter = 0;
            int animExit = 0;
            int animPopEnter = 0;
            int animPopExit = 0;
            if(fragment == errorMessageFragment){
                animEnter = R.anim.enter_from_bottom;
                animExit = R.anim.exit_to_top;
                animPopEnter = R.anim.enter_from_top;
                animPopExit = R.anim.exit_to_bottom;
            }
            else if(currentFragment == errorMessageFragment){
                animEnter = R.anim.enter_from_top;
                animExit = R.anim.exit_to_bottom;
                animPopEnter = R.anim.enter_from_bottom;
                animPopExit = R.anim.exit_to_top;
            }
            else if(forward) {
                if(moveStep)
                    stepsFragment.nextStep();
                animEnter = R.anim.slide_in_right;
                animExit = R.anim.slide_out_left;
                animPopEnter = R.anim.slide_in_right;
                animPopExit = R.anim.slide_out_left;
            }
            else {
                if(moveStep)
                    stepsFragment.previousStep();
                animEnter = R.anim.slide_in_left;
                animExit = R.anim.slide_out_right;
                animPopEnter = R.anim.slide_in_left;
                animPopExit = R.anim.slide_out_right;
            }
            transaction.setCustomAnimations(animEnter, animExit, animPopEnter, animPopExit);
            transaction.replace(R.id.fragment_container, fragment);
            transaction.addToBackStack(null);
            transaction.commitAllowingStateLoss();
        } catch (InterruptedException e) {
            systemError(TAG, e);
        }
    }

    private void cancelTransaction(boolean removeTimer){
        if(removeTimer)
            removeTimer();

        FragmentManager fragmentManager = getSupportFragmentManager();
        Fragment currentFragment = fragmentManager.findFragmentById(R.id.fragment_container);

        if (currentFragment != null) {
            if (currentFragment == preAuthFragment) {
                try {
                    getService().cancelCardTransaction(getPumpNo());
                } catch (InterruptedException e) {
                    Log.e(TAG, e);
                }
            }
            else if (currentFragment == rewardCardFragment) {
                try {
                    getService().cancelCardTransaction();
                } catch (InterruptedException e) {
                    Log.e(TAG, e);
                }
            }
            else if (currentFragment == transactionProcessFragment){
                systemError(TAG, new Exception("Transaction Timed Out"));
                return;
            }
        }

        Intent intent = new Intent(this, BlueHomeActivity.class);
        this.startActivity(intent);
        this.finish();
    }

    private void backTransaction(){
        FragmentManager fragmentManager = getSupportFragmentManager();
        Fragment currentFragment = fragmentManager.findFragmentById(R.id.fragment_container);

        if (currentFragment != null) {
            if (currentFragment == pumpSelectFragment) {
                cancelTransaction(true);
            }
            else if (currentFragment == amountFragment) {
                moveTo(pumpSelectFragment, false, true, timeOut);
            }
            else if (currentFragment == preAuthFragment) {
                moveTo(amountFragment,false, true, timeOut);
            }
            else if (currentFragment == rewardCardFragment) {
                moveTo(rewardCardPromptFragment,false, false, transactionTimeOut);
            }
            else if (currentFragment == odometerFragment) {
                if(capabilities != null && capabilities.isPromptVehicle())
                    moveTo(vehicleFragment,false, true, transactionTimeOut);
            }
        }
    }

    @Override
    public void systemError(String paramString, Throwable paramThrowable) {
        if(paramThrowable != null) {
            Log.e(paramString, paramThrowable);
            runOnUiThread(() -> {

                StringWriter stringWriter = new StringWriter();
                if(paramThrowable!=null) {
                    paramThrowable.printStackTrace(new PrintWriter(stringWriter));
                }
                else {
                    stringWriter.write("FAILED");
                }

                Intent intent = new Intent(this, BlueErrorActivity.class);
                intent.putExtra(BlueErrorActivity.ERROR_MESSAGE, paramThrowable.getMessage());
                intent.putExtra(BlueErrorActivity.ERROR_DETAIL, stringWriter.toString());
                intent.putExtra(BlueErrorActivity.TINT_COLOR, tintColor);
                intent.putExtra(BlueErrorActivity.LOGO_URL, logoUrl);
                intent.putExtra(BlueErrorActivity.CONTACT, contact);
                startActivity(intent);
                finish();
            });
        }
    }

    @Override
    public void serviceReady() {
        runOnUiThread(() -> {
            cancelTransaction(true);
        });
    }

    @Override
    public void cardTransactionDeclined(String bankResponse, String cardMask) {
        runOnUiThread(() -> {
            transactionDeclinedFragment.setBankResponse(bankResponse);
            moveTo(transactionDeclinedFragment, true, false, transactionTimeOut);
        });
    }

    @Override
    public void cardTransactionInProgress(String pumpNo, long processAmount) {
        runOnUiThread(() ->{
            moveTo(transactionProcessFragment, true, false, transactionTimeOut);
        });
    }

    @Override
    public void pumpAuthorised(long trxNo, int pumpNo, String[] authorizedGrades) {
        runOnUiThread(() -> {
            transactionSuccessFragment.setTransactionNo(trxNo);
            transactionSuccessFragment.setPumpNo(pumpNo);
            transactionSuccessFragment.setAmount(amountSelected);
            transactionSuccessFragment.setAuthorizedGrades(authorizedGrades);
            moveTo(transactionSuccessFragment, true, true, timeOut);
        });
    }

    @Override
    public void getWhiteCardUserInformation(long externalReference, String im30Reference, AccountCardCapabilities capabilities){
        this.externalReference = externalReference;
        this.im30Reference = im30Reference;
        this.capabilities = capabilities;
        runOnUiThread(() -> {
            if(capabilities != null) {
                odometerFragment.setRequest(capabilities.isValidateOdometer());
                if (capabilities.isPromptVehicle()) {
                    steps.add(3, "Enter vehicle registration");
                    if(capabilities.isPromptOdometer())
                        steps.add(4, "Enter odometer");
                    moveTo(vehicleFragment, true, true, transactionTimeOut);
                }
                else if (capabilities.isPromptOdometer()) {
                    steps.add(3, "Enter odometer");
                    moveTo(odometerFragment, true, true, transactionTimeOut);
                }
                stepsFragment.setupSteps(steps);
            }
        });
    }

    @Override
    public void getRewardCardUserInformation(long externalReference){
        this.externalReference = externalReference;
        runOnUiThread(() -> {
            steps.add(3, rewardText);
            stepsFragment.setupSteps(steps);
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                Log.e(TAG, e);
            }
            moveTo(rewardCardPromptFragment, true, true, transactionTimeOut);
        });
    }

    @Override
    public void rewardCardApplied(List<RewardCardDiscount> discounts){
        //transactionSuccessFragment.setLitres(discounts.stream().max(Comparator.comparingLong(p -> p.getCap() == null ? 0 : p.getCap())).get().getCap());
        Optional<Long> maxCap = discounts.stream()
                .map(p -> p.getCap() == null ? 0L : p.getCap())
                .max(Long::compare);

        if (maxCap.isPresent()) {
            transactionSuccessFragment.setLitres(maxCap.get());
        }
        proceedPrepaidTransaction();
    }

    @Override
    public void rewardCardDeclined(String message){
        runOnUiThread(() -> {
            rewardDeclinedFragment.setError(message);
            moveTo(rewardDeclinedFragment, true, false, transactionTimeOut);
        });
    }

    @Override
    public void cardMagneticComplete(String track2){
        startTimer(transactionTimeOut);
        getService().validateRewardCard(externalReference, track2);
    }

    @Override
    public void cardMagneticDeclined(String message){
        if(!message.equals("Card Reader Cancelled")) {
            if(message.equals("Card Reader Timeout"))
                rewardDeclinedFragment.setRepeat(false);
            rewardCardDeclined(message);
        }
    }

    @Override
    public void fuellingStatusUpdate(){
        runOnUiThread(() -> {
            fuelPoints = getService().getConfiguredFuelPoints();
            pumpSelectFragment.updateFuelPoints(fuelPoints);
        });
    }

    @Override
    public boolean onKeyDown(int paramInt, KeyEvent paramKeyEvent) {
        if(this.optQRCodeEnabled && paramInt != KeyEvent.KEYCODE_ENTER) {
            appBarFragment.showBack(false);
            appBarFragment.showCancel(false);
            char c = (char) paramKeyEvent.getUnicodeChar();
            barcodeBuffer.append(c);

            barcodeHandler.removeCallbacks(barcodeRunnable);
            barcodeHandler.postDelayed(barcodeRunnable, BARCODE_TIMEOUT);
        }
        return true;
    }

    public void showMessageError(String message) {
        errorMessageFragment.setErrorMessage(message);
        moveTo(errorMessageFragment, true, false, transactionTimeOut);
    }
}