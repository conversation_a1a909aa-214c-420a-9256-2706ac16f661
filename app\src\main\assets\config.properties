# SmartFuel Kiosk Configuration
# This is a basic configuration for testing/development

# Layout configuration
layout_type=default

# Network configuration (enabled for local API testing)
network_enabled=true
offline_mode=false

# API endpoints (configured for local development)
# Use ******** for Android emulator to reach host machine localhost
api_host=********
api_port=7225
api_protocol=http

# Local testing configuration
test_mode=false
demo_mode=false

# Kiosk identification (for local development)
# You can set a specific device ID for testing, or leave empty to use Android ID
kiosk_device_id=TEST_KIOSK_001

# Email configuration (for local development)
smtp_host=localhost
smtp_port=25
smtp_user=test
smtp_password=test
smtp_recipients=test@localhost

# Store and kiosk information
kiosk_name=Local Test Kiosk
kiosk_id=TEST_KIOSK_001
store_name=Local Test Store
store_id=TEST_STORE_001
store_currency=AUD

# UI Configuration
tint_color=#1A588E
logo_url=
contact=

# Fuel point configuration (demo data)
fuel_points=4
grade_count=3

# Demo fuel grades
grade_1_name=Unleaded
grade_1_price=1.65
grade_2_name=Premium
grade_2_price=1.75
grade_3_name=Diesel
grade_3_price=1.55

# Timeout settings
connection_timeout=5000
read_timeout=10000

# Printer settings
printer_enabled=false
receipt_printer_usb=false

# Payment terminal settings
payment_terminal_enabled=false
payment_terminal_type=demo
