package com.smartfuel.ui;

import android.annotation.SuppressLint;

import androidx.appcompat.app.ActionBar;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowInsets;
import android.widget.TextView;

import com.smartfuel.R;
import com.smartfuel.service.OPTService;


/**
 * An example full-screen activity that shows and hides the system UI (i.e.
 * status bar and navigation/system bar) with user interaction.
 */
public class TransactionDeclinedActivity extends BaseActivity {

    OPTService myOPTService;

    private static boolean isOpen = false;

    private ServiceConnection mConnection = new ServiceConnection() {
        public void onServiceConnected(ComponentName componentName, IBinder binder) {
            OPTService.LocalBinder localBinder = (OPTService.LocalBinder)binder;
            myOPTService = localBinder.getServiceInstance();
        }

        public void onServiceDisconnected(ComponentName componentName) {}
    };
    protected void onCreate(Bundle paramBundle) {
        super.onCreate(paramBundle);

        if(isOpen) {
            finish();
            return;
        }
        isOpen = true;

        setContentView(R.layout.activity_transaction_declined);
        TextView proceedPumpView = findViewById(R.id.textProceedtoPump);
        // start process to auto print declined receipt

        proceedPumpView.setText(String.format("Card\n%s \n%s", getIntent().getStringExtra("CardSignature"), getIntent().getStringExtra("BankResponse")));

        (new Handler()).postDelayed(new Runnable() {
            public void run() {
                Intent intent = new Intent((Context)TransactionDeclinedActivity.this, HomeActivity.class);
                TransactionDeclinedActivity.this.startActivity(intent);
                TransactionDeclinedActivity.isOpen = false;
                TransactionDeclinedActivity.this.finish();
            }
        },15000L);
    }
}