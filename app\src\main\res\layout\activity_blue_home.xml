<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.blue.BlueHomeActivity"
    android:orientation="vertical">

    <fragment
        android:id="@+id/fragment_custom_app_bar"
        android:name="com.smartfuel.ui.blue.BlueAppBarFragment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="0dp" />
    
    <TableLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/vwStart"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="3"
            android:orientation="vertical"
            android:gravity="center">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginHorizontal="@dimen/activity_horizontal_margin1">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/welcome"
                    android:fontFamily="@font/gibson_bolditalic"
                    android:gravity="center_horizontal"
                    android:layout_marginBottom="@dimen/margin_bottom1"
                    android:textColor="@color/White"
                    android:textSize="@dimen/text_title1"/>

                <TextView
                    android:id="@+id/txtStore"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/gibson_bold"
                    android:gravity="center_horizontal"
                    android:textSize="@dimen/text_title2"
                    android:textColor="@color/White"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/gibson_regular"
                    android:gravity="center_horizontal"
                    android:textSize="@dimen/text_title2"
                    android:textColor="@color/White"
                    android:text="@string/selfservice"
                    android:layout_marginBottom="@dimen/margin_bottom1"/>

                <com.smartfuel.ui.blue.components.BlueButton2
                    android:id="@+id/btnStart"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/start"
                    android:textSize="@dimen/text_title2"
                    android:paddingVertical="@dimen/padding_button1"/>
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="2"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@color/White">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginHorizontal="@dimen/activity_horizontal_margin1">

                <TextView
                    android:id="@+id/txtReceipt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/gibson_bold"
                    android:gravity="center_horizontal"
                    android:textSize="@dimen/text_title2"
                    android:textColor="@color/Black"
                    android:text="@string/needreceipt"
                    android:layout_marginBottom="@dimen/margin_bottom2"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/gibson_regular"
                    android:gravity="center_horizontal"
                    android:textSize="@dimen/text_title3"
                    android:textColor="@color/blue_black_text"
                    android:text="@string/receiptinfo"
                    android:layout_marginBottom="@dimen/margin_bottom2"/>

                <com.smartfuel.ui.blue.components.BlueButton2
                    android:id="@+id/btnReceipt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/printreceipt"
                    android:textSize="@dimen/text_title2"
                    android:paddingVertical="@dimen/padding_button2"/>
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/layContact"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@color/White"
            android:padding="@dimen/activity_horizontal_margin2"
            android:visibility="gone">

                <TextView
                    android:id="@+id/txtContact"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/gibson_regular"
                    android:gravity="center_horizontal"
                    android:textSize="@dimen/text_size3"
                    android:textColor="@color/blue_black_text"/>

        </LinearLayout>

    </TableLayout>


</LinearLayout>