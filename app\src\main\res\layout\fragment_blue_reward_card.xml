<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal">

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_bolditalic"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title1"
        android:textColor="@color/Black"
        android:layout_marginBottom="@dimen/margin_bottom2"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin4"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_regular"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title3"
        android:textColor="@color/blue_black_text"
        android:text="@string/reward_card_info"
        android:layout_marginBottom="@dimen/margin_bottom2"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin4"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/card_height2"
        android:orientation="horizontal"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin2">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layQr"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <androidx.cardview.widget.CardView
                android:id="@+id/scanCard"
                android:layout_width="0dp"
                android:layout_height="@dimen/card_height1"
                app:cardCornerRadius="@dimen/card_radius"
                app:cardElevation="0dp"
                app:contentPadding="@dimen/padding_button3"
                app:contentPaddingTop="@dimen/padding_button8"
                app:cardBackgroundColor="@color/Black"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/gibson_regular"
                    android:gravity="center_horizontal"
                    android:textSize="@dimen/text_size1"
                    android:textColor="@color/White"
                    android:text="@string/scan_your_digital" />

            </androidx.cardview.widget.CardView>

            <ImageView
                android:id="@+id/imgQrCode"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:src="@drawable/qrcode"
                android:layout_marginBottom="@dimen/margin_offset"
                android:layout_marginHorizontal="@dimen/activity_horizontal_margin5"
                android:maxHeight="@dimen/image_size3"
                app:layout_constraintBottom_toTopOf="@id/scanCard"
                app:layout_constraintStart_toStartOf="@id/scanCard"
                app:layout_constraintEnd_toEndOf="@id/scanCard"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/txtOr"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/gibson_regular"
            android:gravity="center_horizontal"
            android:textSize="@dimen/text_size1"
            android:text="OR"
            android:textColor="@color/Black"
            android:layout_marginLeft="@dimen/margin_offset2"
            android:layout_marginRight="@dimen/margin_offset2"
            android:layout_marginTop="@dimen/margin_bottom1"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <androidx.cardview.widget.CardView
                android:id="@+id/swipeCard"
                android:layout_width="0dp"
                android:layout_height="@dimen/card_height1"
                app:cardCornerRadius="@dimen/card_radius"
                app:cardElevation="0dp"
                app:contentPadding="@dimen/padding_button3"
                app:contentPaddingTop="@dimen/padding_button8"
                app:cardBackgroundColor="@color/Black"
                app:layout_constraintWidth_max="@dimen/card_width"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/gibson_regular"
                    android:gravity="center_horizontal"
                    android:textSize="@dimen/text_size1"
                    android:textColor="@color/White"
                    android:text="@string/swipe_plastic" />

            </androidx.cardview.widget.CardView>

            <ImageView
                android:id="@+id/imgCard"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:src="@drawable/rewardcard"
                android:layout_marginBottom="@dimen/margin_offset"
                android:layout_marginHorizontal="@dimen/activity_horizontal_margin5"
                android:maxHeight="@dimen/image_size3"
                app:layout_constraintBottom_toTopOf="@id/swipeCard"
                app:layout_constraintStart_toStartOf="@id/swipeCard"
                app:layout_constraintEnd_toEndOf="@id/swipeCard"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/margin_bottom1">

        <ImageView
            android:id="@+id/imgleft"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:src="@drawable/seta"
            android:adjustViewBounds="true"/>

        <ImageView
            android:id="@+id/imgRight"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:src="@drawable/seta"
            android:adjustViewBounds="true"
            android:scaleX="-1"/>

    </LinearLayout>

</LinearLayout>