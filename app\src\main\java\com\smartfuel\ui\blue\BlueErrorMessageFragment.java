package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.os.Handler;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.blue.components.BlueButton1;
import com.smartfuel.ui.utils.ImageDownloader;

public class BlueErrorMessageFragment extends Fragment {

    private final int tintColor;
    private final String logoURL;
    private final String contact;

    private String errorMessage;
    private String errorDetail = "";
    private Fragment currentFragment;

    private ImageDownloader imageDownloader;

    private final OnErrorMessageListener listener;

    private Handler myHandler;
    private Runnable myRunnable;

    long timeOut = 5000L;

    public BlueErrorMessageFragment(int tintColor, String logoURL, String contact, OnErrorMessageListener listener) {
        this.tintColor = tintColor;
        this.logoURL = logoURL;
        this.contact = contact;
        this.listener = listener;
    }

    protected void removeTimer(){
        if(myHandler != null)
            myHandler.removeCallbacks(myRunnable);
    }

    protected void setupTimer(long timeOut, Runnable action){
        this.myHandler = new Handler();
        this.myRunnable = () -> {
            myHandler.removeCallbacks(null);
            action.run();
        };
        startTimer(timeOut);
    }

    protected void startTimer(long timeOut){
        removeTimer();
        if(myHandler != null)
            myHandler.postDelayed(myRunnable, timeOut);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_error_message, container, false);

        imageDownloader = new ImageDownloader(getContext());

        BlueButton1 btnBack = view.findViewById(R.id.btnBack);
        btnBack.setBackgroundColor(tintColor);
        btnBack.setOnClickListener(v -> {
            removeTimer();
            listener.onBackClick(currentFragment);
        });

        if(!logoURL.isEmpty()) {
            ImageView logo = view.findViewById(R.id.logo);
            imageDownloader.loadImage(logoURL, bitmap -> {
                if (bitmap != null) {
                    logo.setImageBitmap(bitmap);
                }
            });
        }

        ((TextView)view.findViewById(R.id.txtErrorMessage)).setText(errorMessage);

        if(!errorDetail.isEmpty())
            ((TextView)view.findViewById(R.id.txtDetail)).setText(errorDetail);

        if(!contact.isEmpty()){
            view.findViewById(R.id.layContact).setVisibility(View.VISIBLE);
            ((TextView)view.findViewById(R.id.txtContact)).setText(Html.fromHtml(contact, Html.FROM_HTML_MODE_LEGACY));
        }

        setupTimer(timeOut, () -> listener.onTimeOut(currentFragment));

        return view;
    }

    public void setErrorMessage(String errorMessage){
        this.errorMessage = errorMessage;
    }

    public void setErrorDetail(String errorDetail){
        this.errorDetail = errorDetail;
    }

    public void setCurrentFragment(Fragment currentFragment){
        this.currentFragment = currentFragment;
    }

    public interface OnErrorMessageListener{
        void onTimeOut(Fragment currentFragment);
        void onBackClick(Fragment currentFragment);
    }
}