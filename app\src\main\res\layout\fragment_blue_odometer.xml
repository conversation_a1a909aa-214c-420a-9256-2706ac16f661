<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal">

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_bolditalic"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title1"
        android:textColor="@color/Black"
        android:text="@string/enter_odometer"
        android:layout_marginBottom="@dimen/margin_bottom3"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_regular"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title3"
        android:textColor="@color/blue_black_text"
        android:text="@string/odometer_info"
        android:layout_marginBottom="@dimen/margin_bottom2"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin4"/>

    <fragment
        android:id="@+id/fragment_num_keyboard"
        android:name="com.smartfuel.ui.blue.BlueNumKeyboardFragment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"/>

    <com.smartfuel.ui.blue.components.BlueButton1
        android:id="@+id/btnNext"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/skip"
        android:textSize="@dimen/text_title2"
        android:paddingVertical="@dimen/padding_button2"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin1"
        android:layout_marginBottom="@dimen/margin_bottom1"/>

</LinearLayout>