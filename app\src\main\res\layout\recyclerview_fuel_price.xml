<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="300px"
    android:layout_height="wrap_content"
    android:layout_margin="20px"
    app:cardCornerRadius="40px"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:background="#D8D8D8">

        <TextView
            android:id="@+id/fuelName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/fuel_name"
            android:layout_below="@id/pump_image"
            android:textSize="28px"
            android:textColor="@android:color/white"
            android:background="@color/Blue"
            android:fontFamily="@font/montserrat_semibold"
            android:textStyle="" />

        <TextView
            android:id="@+id/fuelPrice"
            android:fontFamily="@font/montserrat"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/fuel_price_sample"
            android:layout_below="@id/pump_image"
            android:textSize="32px" />
    </LinearLayout>

</androidx.cardview.widget.CardView>