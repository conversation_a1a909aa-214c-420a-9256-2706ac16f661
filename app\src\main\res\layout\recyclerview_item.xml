<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="20px"
    app:cardCornerRadius="80px"
    app:cardElevation="5px"
    >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:background="#D8D8D8">

            <ImageView
                android:id="@+id/pump_image"
                android:layout_width="380px"
                android:layout_height="160px"
                android:paddingTop="40px"
                android:src="@drawable/gas_pump"
                android:layout_alignParentTop="true"/>

            <TextView
                android:id="@+id/info_text"
                android:fontFamily="@font/montserrat"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginVertical="20px"
                android:layout_below="@id/pump_image"
                android:textSize="48px"
                android:textStyle="bold"
                android:layout_alignParentBottom="true"/>
        </LinearLayout>

</androidx.cardview.widget.CardView>