package com.smartfuel.ui;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import com.smartfuel.service.logger.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;

import com.smartfuel.R;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.OPTService;
import com.smartfuel.service.models.kiosk.response.UserResponse;
import com.smartfuel.service.models.transaction.CardReceipt;
import com.smartfuel.service.models.transaction.WhiteCardReceipt;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.List;
import java.util.Locale;

public class PresentPaymentActivity extends BaseActivity implements IServiceEvents {
    protected String cardType = "CARD";
    protected OPTService myOPTService;
    protected long processAmount;
    protected String pumpNo;
    protected String userQRCode = "";

    Handler myHandler;
    Runnable myRunnable;
    long delayScreen = 65000L;

    private void removeTimeOut(){
        try {
            myHandler.removeCallbacks(myRunnable);
        }catch (Exception e){}
    }

    private ServiceConnection mConnection = new ServiceConnection() {

        public void onServiceConnected(ComponentName componentName, IBinder binder) {
            OPTService.LocalBinder localBinder = (OPTService.LocalBinder) binder;
            PresentPaymentActivity.this.myOPTService = localBinder.getServiceInstance();

            try {
                PresentPaymentActivity.this.myOPTService.registerClient(PresentPaymentActivity.this);

                // service will raise an event when the IM30 Terminal responds. Event will navigate user to transaction in progress screen.
                myOPTService.initialiseTransaction(pumpNo, processAmount);

            } catch (Exception e) {
                systemError("Service Unavailable", e);
            }
        }

        public void onServiceDisconnected(ComponentName param1ComponentName) {
        }
    };

    private long trnAmountConversion(String stringAmount) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance(Locale.US);
        long longAmount = 0L;
        if (numberFormat instanceof DecimalFormat)
            ((DecimalFormat) numberFormat).setParseBigDecimal(true);
        try {
            longAmount = numberFormat.parse(stringAmount.replaceAll("[^\\d]", "")).longValue();
        } catch (ParseException parseException) {
            startActivity(new Intent((Context) this, PumpSelectActivity.class));
            finish();
        }
        return longAmount;
    }

    public void customerReceiptDataReady(List<WhiteCardReceipt> paramList) {
    }

    @Override
    public void customerCardReceiptDataReady(List<CardReceipt> cardReceipts) {

    }

    protected void onCreate(Bundle paramBundle) {
        super.onCreate(paramBundle);
        bindService(new Intent((Context) this, OPTService.class), this.mConnection, BIND_AUTO_CREATE);
        setContentView(R.layout.activity_present_payment);

        Button cancelBtn = (Button) this.findViewById(R.id.cancelBtn);

        cancelBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                removeTimeOut();
                try {
                    myOPTService.cancelCardTransaction(pumpNo);
                } catch (InterruptedException e) {
                    systemError("Service Unavailable", e);
                }
                Intent intent = new Intent((Context) PresentPaymentActivity.this, HomeActivity.class);
                PresentPaymentActivity.this.startActivity(intent);
                PresentPaymentActivity.this.finish();
            }
        });

        this.pumpNo = getIntent().getStringExtra("selectedPump");
        this.processAmount = trnAmountConversion(getIntent().getStringExtra("trnAmount"));

        this.myHandler = new Handler();
        this.myRunnable = () -> {
            myHandler.removeCallbacks(null);
            cancelBtn.performClick();
        };
        myHandler.postDelayed(myRunnable, delayScreen);

    }

    public boolean onKeyDown(int paramInt, KeyEvent paramKeyEvent) {
        char c = (char) paramKeyEvent.getUnicodeChar();
        if (c == '|') {
            //this.myHandler.removeCallbacks(this.myRunnable);
            removeTimeOut();
            try {
                myOPTService.cancelCardTransaction(this.pumpNo);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            Intent intent = new Intent((Context) this, TransactionProcessActivity.class);
            this.cardType = "WHITECARD";
            intent.putExtra("cardType", "WHITECARD");
            intent.putExtra("selectedPump", this.pumpNo);
            intent.putExtra("processAmount", this.processAmount);
            intent.putExtra("QRCodeData", this.userQRCode);
            startActivity(intent);
            finish();
        } else if (paramInt != 59) {
            this.userQRCode += c;
        }
        return true;
    }

    public boolean onKeyUp(int paramInt, KeyEvent paramKeyEvent) {
        return true;
    }

    public void serviceReady() {
        removeTimeOut();
        Intent intent = new Intent((Context) this, HomeActivity.class);
        startActivity(intent);
        finish();
    }

    public void systemError(String paramString, Throwable paramThrowable) {
        removeTimeOut();
        Log.e("TransactionActivity", "SystemError", paramThrowable);
        StringWriter stringWriter = new StringWriter();
        paramThrowable.printStackTrace(new PrintWriter(stringWriter));
        Intent intent = new Intent((Context) this, SystemErrorActivity.class);
        intent.putExtra("ErrorMessage", paramString);
        intent.putExtra("ErrorStackTrace", stringWriter.toString());
        startActivity(intent);
        finish();
    }

    public void whiteCardUserRejected(UserResponse paramUserResponseResponse) {
    }

    @Override
    public void cardTransactionDeclined(String bankResponse, String cardMask) {
        removeTimeOut();
        Intent intent = new Intent((Context) this, TransactionDeclinedActivity.class);
        intent.putExtra("CardSignature", cardMask);
        intent.putExtra("BankResponse", bankResponse);
        startActivity(intent);
        finish();

    }

    @Override
    public void cardTransactionInProgress(String pumpNo, long processAmount) {
        removeTimeOut();
        Intent intent = new Intent((Context) this, TransactionProcessActivity.class);
        intent.putExtra("cardType", "CARD");
        intent.putExtra("selectedPump", this.pumpNo);
        intent.putExtra("processAmount", this.processAmount);
        startActivity(intent);
        finish();
    }
}
