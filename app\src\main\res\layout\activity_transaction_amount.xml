<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_transaction"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_background"
    android:fontFamily="@font/montserrat"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:theme="@style/Theme.AppCompat.DayNight.NoActionBar"
    app:behavior_hideable="true"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

    <LinearLayout
        android:id="@+id/infoblock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/imagePumpIcon"
            android:layout_width="123px"
            android:layout_height="123px"
            android:layout_gravity="center"
            android:layout_marginTop="30dp"
            android:layout_weight="1"
            android:contentDescription="@string/pumpimage"
            app:layout_constraintBottom_toTopOf="@+id/pumpNumber"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/gas_pump" />

        <TextView
            android:id="@+id/pumpNumber"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:fontFamily="@font/montserrat_bold"
            android:text="@string/pumpNo"
            android:textSize="50dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/fuelPriceList"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/pumpNumber" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/amountDisplay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingTop="50dp">

        <EditText
            android:id="@+id/editTextAmount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:editable="false"
            android:ems="10"
            android:gravity="center"
            android:hint="@string/amount"
            android:importantForAutofill="no"
            android:textAlignment="center"
            android:textColorHint="#BBBBBB"
            android:textSize="48dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/fuelPriceList" />

        <Button
            android:id="@+id/btnClear"
            style="@style/Button.Gray"
            android:layout_width="200dp"
            android:layout_height="100dp"
            android:textSize="20.0px"
            app:cornerRadius="5.0px"
            android:text="@string/clear"
            app:layout_constraintBottom_toBottomOf="@+id/editTextAmount"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/editTextAmount"
            app:layout_constraintTop_toTopOf="@+id/editTextAmount" />
    </LinearLayout>


    <TableLayout
        android:id="@+id/tableAmountGrid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:stretchColumns="*"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/editTextAmount">

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginVertical="30px"
            android:gravity="center">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/decrease_10"
                    style="@style/Button.Small.Blue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="-"/>

                <TextView
                    android:id="@+id/integer_number_10"
                    style="@style/TextAppearance.Small.Gray"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@string/dlr10"/>

                <Button
                    android:id="@+id/increase_10"
                    style="@style/Button.Small.Blue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="+"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/decrease_20"
                    style="@style/Button.Small.Blue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="-"/>

                <TextView
                    android:id="@+id/integer_number_20"
                    style="@style/TextAppearance.Small.Gray"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@string/dlr20"/>

                <Button
                    android:id="@+id/increase_20"
                    style="@style/Button.Small.Blue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="+"/>
            </LinearLayout>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/decrease_50"
                    style="@style/Button.Small.Blue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="-"/>

                <TextView
                    android:id="@+id/integer_number_50"
                    style="@style/TextAppearance.Small.Gray"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@string/dlr50"/>

                <Button
                    android:id="@+id/increase_50"
                    style="@style/Button.Small.Blue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="+"/>
            </LinearLayout>


        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginVertical="30px"
            android:gravity="center">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/decrease_100"
                    style="@style/Button.Small.Blue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="-"/>

                <TextView
                    android:id="@+id/integer_number_100"
                    style="@style/TextAppearance.Small.Gray"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@string/dlr100"/>

                <Button
                    android:id="@+id/increase_100"
                    style="@style/Button.Small.Blue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="+"/>
            </LinearLayout>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/decrease_200"
                    style="@style/Button.Small.Blue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="-"/>

                <TextView
                    android:id="@+id/integer_number_200"
                    style="@style/TextAppearance.Small.Gray"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@string/dlr200"/>

                <Button
                    android:id="@+id/increase_200"
                    style="@style/Button.Small.Blue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="+"/>
            </LinearLayout>

        </TableRow>
    </TableLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/tableAmountGrid">

        <Button
            android:id="@+id/btnDone"
            style="@style/Button.Gray"
            android:backgroundTint="@color/Blue"
            android:textColor="@color/White"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:text="@string/done_btn"
            android:textSize="32.0px"
            app:cornerRadius="50.0px"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/btnCancel"
            style="@style/Button.Gray"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:text="@string/cancel"
            android:textSize="32.0px"
            app:cornerRadius="50.0px"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </LinearLayout>

</LinearLayout>