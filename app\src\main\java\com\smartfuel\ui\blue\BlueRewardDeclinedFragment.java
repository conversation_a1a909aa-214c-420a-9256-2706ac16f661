package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.blue.components.BlueButton1;
import com.smartfuel.ui.utils.ImageDownloader;

public class BlueRewardDeclinedFragment extends Fragment {

    private final int tintColor;
    private final String rewardText;
    private final boolean enableQr;
    private final String qrUrl;
    private final String cardUrl;

    private String error;
    private boolean repeat = true;

    private ImageDownloader imageDownloader;

    private final DeclinedListener listener;

    private Handler myHandler;
    private Runnable myRunnable;

    long timeOut = 10000L;

    public BlueRewardDeclinedFragment(int tintColor, String rewardText, boolean enableQr, String qrUrl, String cardUrl, DeclinedListener listener) {
        this.tintColor = tintColor;
        this.rewardText = rewardText;
        this.enableQr = enableQr;
        this.qrUrl = qrUrl;
        this.cardUrl = cardUrl;
        this.listener = listener;
    }

    protected void removeTimer(){
        if(myHandler != null)
            myHandler.removeCallbacks(myRunnable);
    }

    protected void setupTimer(long timeOut, Runnable action){
        this.myHandler = new Handler();
        this.myRunnable = () -> {
            myHandler.removeCallbacks(null);
            action.run();
        };
        startTimer(timeOut);
    }

    protected void startTimer(long timeOut){
        removeTimer();
        if(myHandler != null)
            myHandler.postDelayed(myRunnable, timeOut);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_reward_declined, container, false);

        imageDownloader = new ImageDownloader(getContext());

        TextView txtTitle = view.findViewById(R.id.txtTitle);
        txtTitle.setTextColor(tintColor);
        txtTitle.setText(rewardText);

        if(enableQr) {
            if (!qrUrl.isEmpty()) {
                imageDownloader.loadImage(qrUrl, bitmap -> {
                    if (bitmap != null) {
                        ((ImageView) view.findViewById(R.id.imgQrCode)).setImageBitmap(bitmap);
                    }
                });
            }
        }
        else
            view.findViewById(R.id.imgQrCode).setVisibility(View.GONE);

        if(!cardUrl.isEmpty()) {
            imageDownloader.loadImage(cardUrl, bitmap -> {
                if (bitmap != null) {
                    ((ImageView)view.findViewById(R.id.imgCard)).setImageBitmap(bitmap);
                }
            });
        }

        BlueButton1 btnSkip = view.findViewById(R.id.btnSkip);
        btnSkip.setBackgroundColor(tintColor);
        btnSkip.setOnClickListener(v -> {
            removeTimer();
            listener.onCancelClick();
        });

        ((TextView)view.findViewById(R.id.txtError)).setText(error);

        ImageView imgLeft = view.findViewById(R.id.imgleft);
        imgLeft.setColorFilter(tintColor);

        ImageView imgRight = view.findViewById(R.id.imgRight);
        imgRight.setColorFilter(tintColor);

        if(!enableQr) {
            imgLeft.setVisibility(View.INVISIBLE);
            view.findViewById(R.id.imgQrCode).setVisibility(View.GONE);
        }

        setupTimer(timeOut, () -> listener.onTimeOut(repeat));

        return view;
    }

    public void setError(String error){
        this.error = error;
    }

    public void setRepeat(boolean repeat){
        this.repeat = repeat;
    }

    public interface DeclinedListener{
        void onTimeOut(boolean repeat);
        void onCancelClick();
    }
}