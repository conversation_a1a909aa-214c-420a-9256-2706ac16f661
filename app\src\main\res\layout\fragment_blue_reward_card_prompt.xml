<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal">

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_bolditalic"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title1"
        android:textColor="@color/Black"
        android:layout_marginBottom="@dimen/margin_bottom1"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin4"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin5"
        android:gravity="center">

        <ImageView
            android:id="@+id/imgQrCode"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:adjustViewBounds="true"
            android:src="@drawable/qrcode"
            android:layout_marginHorizontal="@dimen/activity_horizontal_margin2"/>

        <ImageView
            android:id="@+id/imgCard"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:adjustViewBounds="true"
            android:src="@drawable/rewardcard"
            android:layout_marginHorizontal="@dimen/activity_horizontal_margin2"/>

    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_weight="1"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin1"
        android:layout_marginBottom="@dimen/margin_bottom1">

        <com.smartfuel.ui.blue.components.BlueButton1
            android:id="@+id/btnYes"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/wc_use_yes"
            android:textSize="@dimen/text_title2"
            android:paddingVertical="@dimen/padding_button2"
            android:layout_marginRight="@dimen/activity_horizontal_half_margin2"/>

        <com.smartfuel.ui.blue.components.BlueButton1
            android:id="@+id/btnNo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/wc_use_no"
            android:textSize="@dimen/text_title2"
            android:paddingVertical="@dimen/padding_button2"
            android:layout_marginLeft="@dimen/activity_horizontal_half_margin2"/>
    </LinearLayout>

</LinearLayout>