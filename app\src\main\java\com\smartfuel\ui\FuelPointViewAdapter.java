package com.smartfuel.ui;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;

import com.smartfuel.R;
import com.smartfuel.service.models.forecourt.FuelPoint;
import java.util.ArrayList;

public class FuelPointViewAdapter extends RecyclerView.Adapter<FuelPointViewAdapter.ViewHolder> {
    private ItemClickListener mClickListener;

    private ArrayList<FuelPoint> mData;

    private LayoutInflater mInflater;

    FuelPointViewAdapter(Context context, ArrayList<FuelPoint> fuelpointList) {
        this.mInflater = LayoutInflater.from(context);
        this.mData = fuelpointList;
    }

    String getItem(int index) {
        return String.valueOf(Integer.parseInt(((FuelPoint)this.mData.get(index)).getId()));
    }

    public int getItemCount() {
        return this.mData.size();
    }

    public void onBindViewHolder(ViewHolder viewHolder, int index) {
        String str = this.mData.get(index).getState();
        index = Integer.parseInt(this.mData.get(index).getId());
        if (str.equals("02H")) {
            viewHolder.myTextView.setBackgroundColor(Color.rgb(0, 234, 0));
            viewHolder.myTextView.setText(String.valueOf(index + "\nReady"));
        } else {
            viewHolder.myTextView.setBackgroundColor(Color.rgb(234, 0, 0));
            viewHolder.myTextView.setEnabled(false);
            viewHolder.myTextView.setClickable(false);
            viewHolder.myTextView.setText(String.valueOf(index + "\nIn-Use"));
        }

    }

    public ViewHolder onCreateViewHolder(ViewGroup paramViewGroup, int paramInt) {
        return new ViewHolder(this.mInflater.inflate(R.layout.recyclerview_item, paramViewGroup, false));
    }

    void setClickListener(ItemClickListener paramItemClickListener) {
        this.mClickListener = paramItemClickListener;
    }

    public static interface ItemClickListener {
        void onItemClick(View param1View, int param1Int);
    }

    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        TextView myTextView;

        ViewHolder(View param1View) {
            super(param1View);
            this.myTextView = (TextView)param1View.findViewById(R.id.info_text);
            param1View.setOnClickListener(this);
        }

        public void onClick(View view) {
            if (FuelPointViewAdapter.this.mClickListener != null && this.myTextView.isEnabled())
                FuelPointViewAdapter.this.mClickListener.onItemClick(view, getAdapterPosition());
        }
    }
}
