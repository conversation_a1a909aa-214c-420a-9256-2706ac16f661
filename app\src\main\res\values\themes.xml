<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.Kiosk" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/DarkBlue</item>
        <item name="colorPrimaryVariant">@color/Blue</item>
        <item name="colorOnPrimary">@color/White</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/LightBlue</item>
        <item name="colorSecondaryVariant">@color/Blue</item>
        <item name="colorOnSecondary">@color/Black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
        <item name="android:actionBarStyle">@style/Theme.MaterialComponents.DayNight.NoActionBar</item>
        <item name="android:windowActionBarOverlay">false</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <style name="Theme.Kiosk.Fullscreen" parent="Theme.Kiosk">
        <item name="android:windowBackground">@color/LightGrey</item>
    </style>

    <style name="ThemeOverlay.Kiosk.FullscreenContainer" parent="">
        <item name="fullscreenBackgroundColor">@color/light_blue_600</item>
        <item name="fullscreenTextColor">@color/light_blue_A200</item>
    </style>
</resources>