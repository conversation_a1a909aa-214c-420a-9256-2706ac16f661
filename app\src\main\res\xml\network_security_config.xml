<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Allow HTTP connections for local development -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- Android emulator host machine -->
        <domain includeSubdomains="false">********</domain>
        <!-- Localhost for testing -->
        <domain includeSubdomains="false">localhost</domain>
        <!-- Local network IP range (adjust as needed) -->
        <domain includeSubdomains="false">***********/24</domain>
        <domain includeSubdomains="false">***********/24</domain>
    </domain-config>
    
    <!-- Default security for production domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
