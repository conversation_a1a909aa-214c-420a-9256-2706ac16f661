package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.utils.ImageDownloader;

public class BlueTransactionProcessFragment extends Fragment {

    private final int tintColor;
    private final String logoURL;

    private TextView txtProcessing;
    private int dotCount = 0;
    private final String baseText = "Processing transaction";

    private Handler handler;

    private ImageDownloader imageDownloader;

    public BlueTransactionProcessFragment(int tintColor, String logoURL) {
        this.tintColor = tintColor;
        this.logoURL = logoURL;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_transaction_process, container, false);

        imageDownloader = new ImageDownloader(getContext());

        ((CardView)view.findViewById(R.id.cardInfo)).setCardBackgroundColor(tintColor);

        if(!logoURL.isEmpty()) {
            ImageView logo = view.findViewById(R.id.logo);
            imageDownloader.loadImage(logoURL, bitmap -> {
                if (bitmap != null) {
                    logo.setImageBitmap(bitmap);
                }
            });
        }

        txtProcessing = view.findViewById(R.id.txtProcessing);
        handler = new Handler();

        startDotAnimation();
        return view;
    }

    private void startDotAnimation() {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                StringBuilder dots = new StringBuilder();
                for (int i = 0; i < dotCount; i++) {
                    dots.append(".");
                }

                txtProcessing.setText(baseText + dots);

                dotCount++;
                if (dotCount > 3) {
                    dotCount = 0;
                }

                handler.postDelayed(this, 500);
            }
        }, 500);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        handler.removeCallbacksAndMessages(null);
    }
}