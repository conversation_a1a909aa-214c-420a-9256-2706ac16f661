package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.blue.components.BlueButton1;
import com.smartfuel.ui.utils.ImageDownloader;

public class BlueRewardCardPromptFragment extends Fragment {

    private final int tintColor;
    private final String rewardText;
    private final boolean enableQr;
    private final String qrUrl;
    private final String cardUrl;

    private final OnClickListener listener;

    private ImageDownloader imageDownloader;

    public BlueRewardCardPromptFragment(int tintColor, String rewardText, boolean enableQr, String qrUrl, String cardUrl, OnClickListener listener) {
        this.tintColor = tintColor;
        this.rewardText = rewardText;
        this.enableQr = enableQr;
        this.qrUrl = qrUrl;
        this.cardUrl = cardUrl;
        this.listener = listener;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_reward_card_prompt, container, false);

        imageDownloader = new ImageDownloader(getContext());

        TextView txtTitle = view.findViewById(R.id.txtTitle);
        txtTitle.setTextColor(tintColor);
        txtTitle.setText(String.format("Do you have a %s?", rewardText));

        if(!qrUrl.isEmpty()) {
            imageDownloader.loadImage(qrUrl, bitmap -> {
                if (bitmap != null) {
                    ((ImageView)view.findViewById(R.id.imgQrCode)).setImageBitmap(bitmap);
                }
            });
        }

        if(!cardUrl.isEmpty()) {
            imageDownloader.loadImage(cardUrl, bitmap -> {
                if (bitmap != null) {
                    ((ImageView)view.findViewById(R.id.imgCard)).setImageBitmap(bitmap);
                }
            });
        }

        if(!enableQr) {
            view.findViewById(R.id.imgQrCode).setVisibility(View.GONE);
        }

        BlueButton1 btnYes = view.findViewById(R.id.btnYes);
        btnYes.setBackgroundColor(tintColor);
        btnYes.setOnClickListener(v -> {
            listener.onClickYes();
        });

        BlueButton1 btnNo = view.findViewById(R.id.btnNo);
        btnNo.setBackgroundColor(tintColor);
        btnNo.setOnClickListener(v -> {
            listener.onClickNo();
        });

        return view;
    }

    public interface OnClickListener{
        void onClickYes();
        void onClickNo();
    }
}