package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.blue.components.BlueButton1;
import com.smartfuel.ui.blue.components.BlueButton2;

public class BlueAmountFragment extends Fragment {

    private final int tintColor;
    private final int maxMoney;
    private final boolean showQuickFuel;
    private final int amountQuickFuel;
    private OnNextCLickListener listener;

    public BlueAmountFragment(int tintColor, int maxMoney, boolean showQuickFuel, int amountQuickFuel, OnNextCLickListener listener) {
        this.tintColor = tintColor;
        this.maxMoney = maxMoney;
        this.showQuickFuel = showQuickFuel;
        this.amountQuickFuel = amountQuickFuel;
        this.listener = listener;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_amount, container, false);

        ((TextView)view.findViewById(R.id.txtTitle)).setTextColor(tintColor);
        BlueNumKeyboardFragment fragment = (BlueNumKeyboardFragment)getChildFragmentManager()
                .findFragmentById(R.id.fragment_num_keyboard);
        fragment.setTintColor(tintColor);
        fragment.setMoney(true);
        fragment.setMaxValue(maxMoney);

        BlueButton1 btnNext = view.findViewById(R.id.btnNext);
        btnNext.setBackgroundColor(tintColor);
        btnNext.setOnClickListener(v -> {
            if(listener != null){
                listener.onNextClick(fragment.getValue());
            }
        });

        BlueButton1 btnQuickFuel = view.findViewById(R.id.btnQuickFuel);
        if(!showQuickFuel)
            btnQuickFuel.setVisibility(View.GONE);
        else {
            btnQuickFuel.setBackgroundColor(tintColor);
            btnQuickFuel.setText("Quick Fuel $" + amountQuickFuel);
            btnQuickFuel.setOnClickListener(v -> {
                if(listener != null){
                    listener.onNextClick(amountQuickFuel);
                }
            });
        }

        return view;
    }

    public interface OnNextCLickListener {
        void onNextClick(int amount);
    }
}