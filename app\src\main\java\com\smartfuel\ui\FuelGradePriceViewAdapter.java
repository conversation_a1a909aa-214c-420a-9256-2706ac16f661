package com.smartfuel.ui;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.smartfuel.R;
import com.smartfuel.service.models.forecourt.GradePrice;

import java.util.ArrayList;
import java.util.stream.Collectors;

public class FuelGradePriceViewAdapter extends RecyclerView.Adapter<FuelGradePriceViewAdapter.ViewHolder> {
    private final ArrayList<GradePrice> mData = new ArrayList<GradePrice>();

    private final LayoutInflater mInflater;

    FuelGradePriceViewAdapter(Context context, ArrayList<GradePrice> gradePriceList) {
        this.mInflater = LayoutInflater.from(context);
        this.mData.addAll(gradePriceList.stream().filter(gp -> gp.getPrice()>0).collect(Collectors.toList())) ;
    }

    public int getItemCount() {
        return this.mData.size();
    }

    public void onBindViewHolder(ViewHolder viewHolder, int index) {
        viewHolder.fuelName.setText(((GradePrice)this.mData.get(index)).getName());
        viewHolder.fuelPrice.setText(((GradePrice)this.mData.get(index)).getPrice() + " c/L");
        String gradeId = ((GradePrice)this.mData.get(index)).getId();
        switch (gradeId){
            case "02":
                viewHolder.fuelName.setBackgroundColor(Color.rgb(234, 194, 69));
                break;
            case "03":
                viewHolder.fuelName.setBackgroundColor(Color.rgb(172, 132, 43));
                break;
            case "04":
                viewHolder.fuelName.setBackgroundColor(Color.rgb(90, 131, 97));
                break;
            case "05":
                viewHolder.fuelName.setBackgroundColor(Color.rgb(231, 73, 61));
                break;
            case "06":
                viewHolder.fuelName.setBackgroundColor(Color.rgb(71, 75, 86));
                break;
            case "07":
                viewHolder.fuelName.setBackgroundColor(Color.rgb(165, 42, 42));
                break;
            case "08":
                viewHolder.fuelName.setBackgroundColor(Color.rgb(17, 49, 133));
                break;
            case "09":
                viewHolder.fuelName.setBackgroundColor(Color.rgb(119, 157, 255));
                break;

        }
    }

    public ViewHolder onCreateViewHolder(ViewGroup paramViewGroup, int paramInt) {
        return new ViewHolder(this.mInflater.inflate(R.layout.recyclerview_fuel_price, paramViewGroup, false));
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        TextView fuelName;

        TextView fuelPrice;

        ViewHolder(View view) {
            super(view);
            this.fuelName = view.findViewById(R.id.fuelName);
            this.fuelPrice = view.findViewById(R.id.fuelPrice);
        }
    }
}
