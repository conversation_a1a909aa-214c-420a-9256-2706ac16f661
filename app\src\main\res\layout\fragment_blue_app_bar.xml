<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/viewBackground"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/app_bar_logo_margin"
        android:paddingHorizontal="@dimen/activity_horizontal_margin2">

        <com.smartfuel.ui.blue.components.BlueButton1
            android:id="@+id/button_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Back"
            android:contentDescription="@string/back_button"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintVertical_bias="0.5"
            android:textSize="@dimen/button_size_1"
            android:paddingHorizontal="@dimen/padding_button4"
            android:paddingVertical="@dimen/padding_button3"/>

        <ImageView
            android:id="@+id/logo"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/app_bar_logo"
            android:adjustViewBounds="true"
            android:src="@drawable/sflogo_dark"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintVertical_bias="0.5"/>

        <com.smartfuel.ui.blue.components.BlueButton1
            android:id="@+id/button_promo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Instructions"
            android:contentDescription="Instructions Button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintVertical_bias="0.5"
            android:textSize="@dimen/button_size_1"
            android:paddingHorizontal="@dimen/padding_button4"
            android:paddingVertical="@dimen/padding_button3"
            android:visibility="gone"/>

        <com.smartfuel.ui.blue.components.BlueButton1
            android:id="@+id/button_done"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/done"
            android:contentDescription="Done Button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintVertical_bias="0.5"
            android:textSize="@dimen/button_size_1"
            android:paddingHorizontal="@dimen/padding_button4"
            android:paddingVertical="@dimen/padding_button3"
            android:visibility="gone"/>

        <com.smartfuel.ui.blue.components.BlueButton1
            android:id="@+id/button_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Cancel"
            android:contentDescription="@string/cancel_button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintVertical_bias="0.5"
            android:textSize="@dimen/button_size_1"
            android:paddingHorizontal="@dimen/padding_button4"
            android:paddingVertical="@dimen/padding_button3"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="2dp"
        android:background="@color/White"/>
</LinearLayout>
