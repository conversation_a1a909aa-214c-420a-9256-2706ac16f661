<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@color/White">

    <ImageView
        android:id="@+id/logoImageView"
        android:layout_width="150dp"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:src="@drawable/sflogo"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@id/receiptTextView"
        android:layout_width="fill_parent"
        android:layout_height="100.0dip"
        android:fontFamily="@font/montserrat_semibold"
        android:gravity="center|top"
        android:text="Processing Transaction\nPlease Wait"
        android:textColor="@color/DarkGrey"
        android:textSize="30.0dip"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_margin="100dp">

        <ImageView
            android:id="@id/bankCard"
            android:layout_width="400.0px"
            android:layout_height="400.0px"
            android:layout_marginTop="70.0dip"
            android:layout_weight="1.0"
            android:contentDescription="@string/qrImage"
            android:layout_gravity="center_horizontal"
            app:srcCompat="@drawable/fleet_card"
            android:visibility="gone"/>

        <LinearLayout
            android:id="@+id/wc_vehicleRegistration"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="left"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Vehicle Registration Number"
                style="@style/Description.Subtitle"/>
            <EditText
                android:id="@+id/Txt_vehicleRegistration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:hint="Tap to enter Registration"
                android:width="800dp"
                android:ems="500"
                android:textSize="65sp"
                android:textAllCaps="true"
                android:inputType="textCapCharacters"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/wc_vehicleOdometer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="left"
            android:visibility="gone">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Vehicle Odometer"
                style="@style/Description.Subtitle"/>
            <EditText
                android:id="@+id/Txt_vehicleOdometer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:hint="Tap to enter Odometer"
                android:width="800dp"
                android:ems="500"
                android:textSize="65sp"
                android:inputType="number"/>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/rcQuestionGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone">
        <androidx.cardview.widget.CardView
            android:layout_width="350dp"
            android:layout_height="wrap_content"
            app:cardCornerRadius="10dp"
            app:cardElevation="10dp"
            app:cardBackgroundColor="#c79845"
            android:layout_gravity="center_horizontal">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Rewards Card"
                    android:textAlignment="center"
                    android:textSize="35dp"
                    android:fontFamily="@font/montserrat_bold"
                    android:textColor="@color/White"
                    android:layout_margin="30dp"/>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@color/Black"
                    android:layout_marginBottom="20dp"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>
        <TextView
            android:id="@+id/txtMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Would you like to use a Reward Card?"
            android:textAlignment="center"
            android:textSize="40dp"
            android:layout_margin="20dp"/>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_gravity="center_horizontal">
            <Button
                android:id="@+id/rc_yes"
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                style="@style/Button.Green"
                android:paddingVertical="30.0px"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:layout_marginRight="30dp"
                android:text="@string/wc_use_yes"
                android:textSize="48.0px"
                app:cornerRadius="80.0px"/>
            <Button
                android:id="@+id/rc_no"
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                style="@style/Button.Red"
                android:paddingVertical="30.0px"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:text="@string/wc_use_no"
                android:textSize="48.0px"
                app:cornerRadius="80.0px"/>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/rcSuccessGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone">
        <androidx.cardview.widget.CardView
            android:layout_width="350dp"
            android:layout_height="wrap_content"
            app:cardCornerRadius="10dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="#c79845"
            android:layout_gravity="center_horizontal">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Rewards Card"
                    android:textAlignment="center"
                    android:textSize="35dp"
                    android:fontFamily="@font/montserrat_bold"
                    android:textColor="@color/White"
                    android:layout_margin="30dp"/>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@color/Black"
                    android:layout_marginBottom="20dp"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>
        <View
            android:layout_width="120dp"
            android:layout_height="100dp"
            android:background="@color/White"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="-80dp"/>

        <ImageView
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:src="@drawable/successful"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="-130dp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAlignment="center"
            android:text="Reward Applied!"
            android:textSize="40dp"
            android:layout_margin="10dp"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/navigationStrip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.91"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.96" >
        <Button
            android:id="@+id/doneBtn"
            style="@style/Button.Gray"
            android:backgroundTint="@color/Blue"
            android:textColor="@color/White"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:text="@string/done_btn"
            android:textSize="32.0px"
            app:cornerRadius="50.0px"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
        <Button
            android:id="@id/cancelBtn"
            style="@style/Button.Gray"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:text="@string/cancel"
            android:textSize="32.0px"
            app:cornerRadius="50.0px"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>