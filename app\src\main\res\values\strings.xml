<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">SmartFuel Terminal</string>
    <string name="smartfuel_logo">SmartFuel logo</string>
    <string name="ellipse">Ellipse</string>
    <string name="date">September 10, 2022</string>
    <string name="_00_00_00">00:00:00</string>
    <string name="am">AM</string>
    <string name="instruction1">Pre-Pay Here</string>
    <string name="instruction2">Fuel at Pump</string>
    <string name="instruction3">Safe Travels</string>
    <string name="_247logo">247Logo</string>
    <string name="touch_icon">Tap to Pay</string>
    <string name="qrImage">QRCode</string>
    <string name="qrDescription">Follow payment terminal instructions below</string>
    <string name="cancel">CANCEL</string>
    <string name="select_your_pump">Select your pump</string>
    <string name="thank_you_messages">Final fuel amount will be charged on card.</string>
    <string name="pump_instruction">Proceed to the pump to start fuelling</string>
    <string name="ticket_no">Ticket No. #0001</string>
    <string name="fuel_name">Fuel Name</string>
    <string name="fuel_price_sample">$2.10</string>
    <string name="pumpimage">Pump</string>
    <string name="amount">Amount</string>
    <string name="amountDesc">Amount:</string>
    <string name="dlr10">$10</string>
    <string name="dlr20">$20</string>
    <string name="dlr50">$50</string>
    <string name="dlr100">$100</string>
    <string name="dlr200">$200</string>
    <string name="done_btn">DONE</string>
    <string name="clear">CLEAR</string>
    <string name="pumpNo">1</string>
    <string name="title_activity_transaction_declined">TransactionDeclinedActivity</string>
    <string name="title_activity_standby">StandbyActivity</string>
    <string name="wc_use_yes">Yes</string>
    <string name="wc_use_no">No</string>
    <string name="wc_instruction">Are you paying by local account card?</string>
    <string name="supportDetail">Support: (02) 8527 0622\nPowered by smartfuelpos.com</string>
    <string name="transaction_failed">Transaction Failed</string>
    <string name="error">Error</string>
    <string name="success">Success</string>
    <string name="purchaseRetry">Please re-try your fuel purchase.</string>
    <string name="back_button">Back Button.</string>
    <string name="cancel_button">Cancel Button.</string>
    <string name="welcome">Welcome!</string>
    <string name="start">Start</string>
    <string name="next">Next</string>
    <string name="close">Close</string>
    <string name="skip">Skip</string>
    <string name="done">Done</string>
    <string name="back">Back</string>
    <string name="email_receipt">Email receipt</string>
    <string name="needreceipt">Need a receipt?</string>
    <string name="receiptinfo">After refuelling, return here and press the button below.</string>
    <string name="selfservice">is self-service where you need to pre-pay for your fuel.</string>
    <string name="printreceipt">Print or email receipt</string>
    <string name="select_pump_number">Select pump number</string>
    <string name="enter_amount">Enter $ amount</string>
    <string name="enter_odometer">Enter your odometer</string>
    <string name="enter_vehicle_registration">Enter your vehicle registration</string>
    <string name="pre_auth_payment">Pre-auth payment</string>
    <string name="time_to_fuel">It’s time\nto fuel up!</string>
    <string name="select_pump_info">Make sure you are parked at this pump already.</string>
    <string name="reward_card_info">Your fuel discount will be applied.</string>
    <string name="pre_auth_info">will be pre-authorised on your chosen payment card.</string>
    <string name="odometer_info">Your card requires/suggests your current odometer reading.</string>
    <string name="receipt_info2">Choose how you would like your receipt provided.</string>
    <string name="receipt_info_email">Enter your email address below.</string>
    <string name="fuel_up_info">&#8226; You will only be charged for the fuel you fill.\n&#8226; Any remaining funds will be automatically released by your bank with 24 - 48 hrs.</string>
    <string name="authorized_info">Your card fuel grades</string>
    <string name="card_info">Tap, insert or swipe your payment card on the payment terminal below.</string>
    <string name="receipt_info">Tap, insert or swipe the card used to pay on the payment terminal below.</string>
    <string name="cards_accepted">Cards accepted</string>
    <string name="processing_transaction">Processing transaction</string>
    <string name="please_wait">Please wait...</string>
    <string name="error_info">Please try again on the payment terminal below.</string>
    <string name="cancel_transaction">Cancel transaction</string>
    <string name="proceed_to_pump">Proceed to pump</string>
    <string name="amount_pre_authorised">has been pre-authorised.</string>
    <string name="transaction_number">Transaction #</string>
    <string name="scan_your_digital">Scan your digital card’s QR code using the scanner below.</string>
    <string name="swipe_plastic">Swipe your plastic card using the magnetic stripe below.</string>
    <string name="try_again">Try again!</string>
    <string name="discount_applied">Your fuel discount has been applied</string>
    <string name="receipt">Receipt</string>
    <string name="dont_recognise">We don’t recognise this payment card.</string>
    <string name="please_try_again">Please try again.</string>
</resources>