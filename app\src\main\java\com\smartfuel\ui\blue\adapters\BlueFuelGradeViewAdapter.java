package com.smartfuel.ui.blue.adapters;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.smartfuel.R;
import com.smartfuel.service.models.forecourt.GradePrice;

import java.util.ArrayList;

public class BlueFuelGradeViewAdapter extends RecyclerView.Adapter<BlueFuelGradeViewAdapter.ViewHolder> {
    private final ArrayList<GradePrice> mData;

    private final LayoutInflater mInflater;

    private final String[] gradeColors;

    public BlueFuelGradeViewAdapter(Context context, ArrayList<GradePrice> gradePriceList, String gradeColors) {
        this.mInflater = LayoutInflater.from(context);
        this.mData = gradePriceList;
        if(gradeColors.isEmpty())
            this.gradeColors = null;
        else
            this.gradeColors = gradeColors.split("\\,");
    }

    public int getItemCount() {
        return this.mData.size();
    }

    public void onBindViewHolder(ViewHolder viewHolder, int index) {
        viewHolder.fuelName.setText(this.mData.get(index).getName());
        if(gradeColors == null)
            viewHolder.layBackground.setBackgroundColor(this.mData.get(index).getColor());
        else
            viewHolder.layBackground.setBackgroundColor(Color.parseColor(gradeColors[Integer.parseInt(this.mData.get(index).getId())-1]));
    }

    public ViewHolder onCreateViewHolder(ViewGroup paramViewGroup, int paramInt) {
        return new ViewHolder(this.mInflater.inflate(R.layout.recyclerview_blue_fuel_grade, paramViewGroup, false));
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        TextView fuelName;

        View layBackground;

        ViewHolder(View view) {
            super(view);
            this.fuelName = view.findViewById(R.id.fuelName);
            this.layBackground = view.findViewById(R.id.layBackground);
        }
    }
}
