package com.smartfuel.ui.blue.components;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.GradientDrawable;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.CallSuper;
import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import androidx.core.content.res.ResourcesCompat;

import com.smartfuel.R;
import com.smartfuel.ui.utils.DrawableUtil;

public abstract class BaseBlueButton extends AppCompatButton {

    protected abstract int getButtonBackgroundColor();
    protected abstract int getButtonBorderColor();
    protected abstract int getButtonTextColor();

    protected float getCornerRadius(){
        return 20f;
    }

    protected int getBorderWidth(){
        return 2;
    }

    private GradientDrawable buttonBackground;

    public BaseBlueButton(@NonNull Context context) {
        super(context);
        setup();
    }

    public BaseBlueButton(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        setup();
    }

    public BaseBlueButton(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setup();
    }

    @SuppressLint("ClickableViewAccessibility")
    private void setup(){
        buttonBackground = DrawableUtil.createButtonBackground(getButtonBackgroundColor(), getButtonBorderColor(), getCornerRadius(), getBorderWidth());
        setTextColor(getButtonTextColor());
        setAllCaps(false);
        setElevation(0);
        setStateListAnimator(null);
        setBackground(buttonBackground);

        Typeface typeface = ResourcesCompat.getFont(getContext(), R.font.gibson_bolditalic);
        setTypeface(typeface);

        setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    int darkenedColor = DrawableUtil.darkenColor(getButtonBackgroundColor(), 0.8f);
                    buttonBackground.setColor(darkenedColor);
                    break;
                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    buttonBackground.setColor(getButtonBackgroundColor());
                    break;
            }
            return false;
        });
    }

    @Override
    public void setBackgroundColor(@ColorInt int color){
        //super.setBackgroundColor(color);
        buttonBackground.setColor(color);
    }

    public void setBorderColor(@ColorInt int color){
        buttonBackground.setStroke(getBorderWidth(), color);
    }
}
