<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="app_bar_logo">50dp</dimen>
    <dimen name="app_bar_logo_margin">5dp</dimen>
    <dimen name="indicator_size">30dp</dimen>
    <dimen name="indicator_num">10dp</dimen>
    <dimen name="indicator_text">6dp</dimen>
    <dimen name="indicator_margin">0dp</dimen>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_horizontal_margin1">45dp</dimen>
    <dimen name="activity_horizontal_margin2">10dp</dimen>
    <dimen name="activity_horizontal_half_margin2">5dp</dimen>
    <dimen name="activity_horizontal_margin3">20dp</dimen>
    <dimen name="activity_horizontal_margin4">35dp</dimen>
    <dimen name="activity_horizontal_margin5">40dp</dimen>
    <dimen name="activity_horizontal_margin6">80dp</dimen>
    <dimen name="activity_vertical_margin1">15dp</dimen>
    <dimen name="margin_bottom1">25dp</dimen>
    <dimen name="margin_bottom2">25dp</dimen>
    <dimen name="margin_bottom3">10dp</dimen>
    <dimen name="text_title1">40dp</dimen>
    <dimen name="text_title2">25dp</dimen>
    <dimen name="text_title3">20dp</dimen>
    <dimen name="text_size1">20dp</dimen>
    <dimen name="text_size2">25dp</dimen>
    <dimen name="text_size3">15dp</dimen>
    <dimen name="text_size4">35dp</dimen>
    <dimen name="text_size5">10dp</dimen>
    <dimen name="button_size_1">20dp</dimen>
    <dimen name="padding_button1">20dp</dimen>
    <dimen name="padding_button2">20dp</dimen>
    <dimen name="padding_button3">10dp</dimen>
    <dimen name="padding_button4">20dp</dimen>
    <dimen name="padding_button5">30dp</dimen>
    <dimen name="padding_button6">30dp</dimen>
    <dimen name="padding_button7">3dp</dimen>
    <dimen name="padding_button8">40dp</dimen>
    <dimen name="padding_button9">20dp</dimen>
    <dimen name="card_radius">10dp</dimen>
    <dimen name="card_radius2">5dp</dimen>
    <dimen name="image_size1">30dp</dimen>
    <dimen name="image_size2">40dp</dimen>
    <dimen name="image_size3">100dp</dimen>
    <dimen name="min_width">100dp</dimen>
    <dimen name="key_width">60dp</dimen>
    <dimen name="key_height">50dp</dimen>
    <dimen name="margin_offset">-20dp</dimen>
    <dimen name="margin_offset2">-5dp</dimen>
    <dimen name="card_height1">130dp</dimen>
    <dimen name="card_height2">210dp</dimen>
    <dimen name="card_width">250dp</dimen>
</resources>