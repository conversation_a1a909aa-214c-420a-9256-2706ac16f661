package com.smartfuel.ui;

import android.content.DialogInterface;
import android.os.Bundle;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Handler;
import android.os.IBinder;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;

import com.smartfuel.R;
import com.smartfuel.service.logger.Log;
import com.smartfuel.service.models.kiosk.response.AccountCardCapabilities;
import com.smartfuel.service.models.kiosk.response.UserResponse;
import com.smartfuel.service.models.transaction.CardReceipt;
import com.smartfuel.service.models.transaction.WhiteCardReceipt;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.OPTService;
import com.smartfuel.service.sqlite.models.RewardCardDiscount;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.List;

public class TransactionProcessActivity extends BaseActivity implements IServiceEvents {
    protected String cardType;
    private long currentTrxId;
    private String im30Reference;
    protected long processAmount;
    protected String pumpNo;
    protected AccountCardCapabilities capabilities;

    Handler myHandler;
    Runnable myRunnable;
    long delayScreen = 60000L;

    private ServiceConnection mConnection = new ServiceConnection() {
        public void onServiceConnected(ComponentName componentName, IBinder binder) {
            try {
                TransactionProcessActivity myTransactionProcessActivity = TransactionProcessActivity.this;

                OPTService.LocalBinder localBinder = (OPTService.LocalBinder) binder;

                myTransactionProcessActivity.myOPTService = localBinder.getServiceInstance();
                myTransactionProcessActivity.myOPTService.registerClient(TransactionProcessActivity.this);
                myTransactionProcessActivity.cardType = myTransactionProcessActivity.getIntent().getStringExtra("cardType");

                switch (myTransactionProcessActivity.cardType) {
                    case "WHITECARD":
                        myTransactionProcessActivity.selectedFuelPoint = myTransactionProcessActivity.getIntent().getStringExtra("selectedPump");
                        myTransactionProcessActivity.processingAmount = myTransactionProcessActivity.getIntent().getLongExtra("processAmount", 0L);
                        myTransactionProcessActivity.myOPTService
                                .AuthoriseWhiteCardTransaction(
                                        myTransactionProcessActivity.getIntent().getStringExtra("QRCodeData"),
                                        myTransactionProcessActivity.selectedFuelPoint,
                                        myTransactionProcessActivity.processingAmount);
                        break;
                    case "CARD":
                        break;
                }
            } catch (Exception e) {
                systemError("Transaction Failed", e);
            }


        }

        public void onServiceDisconnected(ComponentName param1ComponentName) {
        }
    };

    OPTService myOPTService;

    protected long processingAmount;

    protected String selectedFuelPoint;

    public void customerReceiptDataReady(List<WhiteCardReceipt> paramList) {

    }

    @Override
    public void customerCardReceiptDataReady(List<CardReceipt> cardReceipts) {

    }

    private void removeTimeOut(){
        try {
            myHandler.removeCallbacks(myRunnable);
        }catch (Exception e){}
    }

    private void restartTimeOut(){
        removeTimeOut();
        myHandler.postDelayed(myRunnable, delayScreen);
    }

    protected void onCreate(Bundle paramBundle) {
        super.onCreate(paramBundle);
        bindService(new Intent((Context) this, OPTService.class), this.mConnection, BIND_AUTO_CREATE);
        setContentView(R.layout.activity_transaction_process);

        Button cancelBtn = (Button) this.findViewById(R.id.cancelBtn);
        Button whitecardDone = (Button) this.findViewById(R.id.doneBtn);
        EditText wcRegoTxt = this.findViewById(R.id.Txt_vehicleRegistration);
        EditText wcOdoTxt = this.findViewById(R.id.Txt_vehicleOdometer);

        this.myHandler = new Handler();
        this.myRunnable = () -> {
            myHandler.removeCallbacks(null);
            systemError("Transaction Timed Out", new Exception("Transaction Timed Out"));
        };
        myHandler.postDelayed(myRunnable, delayScreen);

        whitecardDone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                restartTimeOut();
                String regoTxt = wcRegoTxt.getText().toString().trim();
                String odoTxt = wcOdoTxt.getText().toString().trim();
                //validate the input to ensure not null or empty - alert if left blank
                capabilities.setInputVehicleRegistration(regoTxt);
                capabilities.setInputOdometer(odoTxt);

                if(!capabilities.validateVehicle())
                    showAlertMessage("Vehicle Details", "Vehicle Registration is invalid.");
                else if(!capabilities.validateOdometer())
                    showAlertMessage("Vehicle Details", "Odometer is invalid.");
                else {
                    showInstructionLayout(false);
                    //myOPTService.initialiseWhiteCardTransaction(pumpNo, processAmount,regoTxt ,odoTxt);
                    TransactionProcessActivity.this.myOPTService.validateTransaction(currentTrxId, im30Reference, capabilities);
                }
            }
        });

        cancelBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                removeTimeOut();
                try {
                    myOPTService.cancelCardTransaction(pumpNo);
                } catch (InterruptedException e) {
                    systemError("Service Unavailable", e);
                }
                Intent intent = new Intent((Context) TransactionProcessActivity.this, HomeActivity.class);
                TransactionProcessActivity.this.startActivity(intent);
                TransactionProcessActivity.this.finish();
            }
        });

        this.pumpNo = getIntent().getStringExtra("selectedPump");
        this.processAmount = getIntent().getLongExtra("trnAmount", 0);
    }

    public void pumpAuthorised(long paramLong, int paramInt, String[] authorizedGrades) {
        removeTimeOut();
        Log.i("pumpAuthorised", "TransactionId: " + String.valueOf(paramLong));
        Log.i("pumpAuthorised", "FuelPointId:" + String.valueOf(paramInt));
        Intent intent = new Intent((Context) this, TransactionSucessActivity.class);
        intent.putExtra("trnId", String.valueOf(paramLong));
        intent.putExtra("fuelPointId", String.valueOf(paramInt));
        intent.putExtra("authorizedGrades", authorizedGrades);
        startActivity(intent);
        finish();
    }

    public void serviceReady() {

    }

    public void systemError(String paramString, Throwable paramThrowable) {
        removeTimeOut();
        Log.e("TransactionActivity", "SystemError", paramThrowable);
        StringWriter stringWriter = new StringWriter();
        paramThrowable.printStackTrace(new PrintWriter(stringWriter));
        Intent intent = new Intent((Context) this, SystemErrorActivity.class);
        intent.putExtra("ErrorMessage", paramString);
        intent.putExtra("ErrorStackTrace", stringWriter.toString());
        startActivity(intent);
        finish();
    }

    public void whiteCardUserRejected(UserResponse paramUserResponseResponse) {

    }

    @Override
    public void cardTransactionDeclined(String bankResponse, String cardMask) {
        removeTimeOut();
        Intent intent = new Intent((Context) this, TransactionDeclinedActivity.class);
        intent.putExtra("CardSignature", cardMask);
        intent.putExtra("BankResponse", bankResponse);
        intent.putExtra("CardType",getIntent().getStringExtra("cardType"));
        startActivity(intent);
        finish();
    }

    @Override
    public void cardTransactionInProgress(String pumpNo, long processAmount) {

    }

    @Override
    public void getWhiteCardUserInformation(long externalReference, String im30Reference, AccountCardCapabilities capabilities){
        this.currentTrxId = externalReference;
        this.im30Reference = im30Reference;
        this.capabilities = capabilities;
        runOnUiThread(() -> {
            showWhitecardLayout();
        });
    }

    @Override
    public void getRewardCardUserInformation(long externalReference){
        this.currentTrxId = externalReference;
        openReward("");
    }

    @Override
    public void rewardCardApplied(List<RewardCardDiscount> discounts){
        restartTimeOut();
        runOnUiThread(() -> {
            showRewardCardSuccess();

            final Handler handler  = new Handler();
            final Runnable runnable = () -> {
                handler.removeCallbacksAndMessages(null);
                try {
                    myOPTService.ProceedPrepaidTransaction(currentTrxId);
                } catch (InterruptedException e) {
                    systemError("Error to proceed prepaid transaction.", e);
                }
            };

            handler.postDelayed(runnable, 3000);
        });
    }

    @Override
    public void rewardCardDeclined(String message){
        openReward(message + "\nWould you like to try again?");
    }

    @Override
    public void cardMagneticComplete(String track2){
        restartTimeOut();
        myOPTService.validateRewardCard(currentTrxId, track2);
    }

    @Override
    public void cardMagneticDeclined(String message){
        rewardCardDeclined(message);
    }

    private void openReward(String message){
        restartTimeOut();
        runOnUiThread(() -> {
            showRewardCardLayout();

            TextView txtMessage = findViewById(R.id.txtMessage);
            Button btnYes = findViewById(R.id.rc_yes);
            Button btnNo = findViewById(R.id.rc_no);

            if(!message.isEmpty())
                txtMessage.setText(message);

            btnYes.setOnClickListener((b) -> {
                showInstructionLayout(true);
                try {
                    myOPTService.initialiseMagnetic();
                } catch (InterruptedException e) {
                    systemError("Error to read reward card.", e);
                }
            });

            btnNo.setOnClickListener((b) -> {
                showInstructionLayout(false);
                try {
                    myOPTService.ProceedPrepaidTransaction(currentTrxId);
                } catch (InterruptedException e) {
                    systemError("Error to proceed prepaid transaction.", e);
                }
            });
        });
    }

    private void showAlertMessage(String title, String message){
        AlertDialog.Builder builder = new AlertDialog.Builder((Context)this);
        builder.setTitle(title).setMessage(message).setCancelable(false).setPositiveButton("OK", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface param1DialogInterface, int param1Int) {}
        });
        builder.create().show();
    }

    private void showInstructionLayout(boolean readCard){
        restartTimeOut();
        TextView receiptTextView = this.findViewById(R.id.receiptTextView);
        Button doneBtn = this.findViewById(R.id.doneBtn);
        Button cancelBtn = (Button) this.findViewById(R.id.cancelBtn);
        LinearLayout wcRego = this.findViewById(R.id.wc_vehicleRegistration);
        LinearLayout wcOdo = this.findViewById(R.id.wc_vehicleOdometer);
        View bankCard = this.findViewById(R.id.bankCard);
        ImageView logoImageView = this.findViewById(R.id.logoImageView);
        View rcQuestionGroup = this.findViewById(R.id.rcQuestionGroup);
        View rcSuccessGroup = this.findViewById(R.id.rcSuccessGroup);

        if(readCard)
            receiptTextView.setText(R.string.qrDescription);
        else
            receiptTextView.setText("Processing Transaction\nPlease Wait");
        receiptTextView.setVisibility(View.VISIBLE);
        logoImageView.setVisibility(View.VISIBLE);
        wcRego.setVisibility(View.GONE);
        wcOdo.setVisibility(View.GONE);
        bankCard.setVisibility(View.GONE);
        doneBtn.setVisibility(View.GONE);
        cancelBtn.setVisibility(View.GONE);
        rcQuestionGroup.setVisibility(View.GONE);
        rcSuccessGroup.setVisibility(View.GONE);
    }

    private void showWhitecardLayout() {
        restartTimeOut();
        LinearLayout wcRego = this.findViewById(R.id.wc_vehicleRegistration);
        LinearLayout wcOdo = this.findViewById(R.id.wc_vehicleOdometer);
        View bankCard = this.findViewById(R.id.bankCard);
        EditText wcRegoTxt = this.findViewById(R.id.Txt_vehicleRegistration);
        ImageView logoImageView = this.findViewById(R.id.logoImageView);

        TextView receiptTextView = this.findViewById(R.id.receiptTextView);
        Button doneBtn = this.findViewById(R.id.doneBtn);
        Button cancelBtn = (Button) this.findViewById(R.id.cancelBtn);
        View rcQuestionGroup = this.findViewById(R.id.rcQuestionGroup);
        View rcSuccessGroup = this.findViewById(R.id.rcSuccessGroup);

        wcRegoTxt.setShowSoftInputOnFocus(true);
        wcRegoTxt.setFocusable(true);
        wcRegoTxt.setFocusableInTouchMode(true);
        wcRegoTxt.requestFocus();

        receiptTextView.setVisibility(View.GONE);
        logoImageView.setVisibility(View.GONE);

        bankCard.setVisibility(View.VISIBLE);
        if(capabilities != null && capabilities.isPromptVehicle())
            wcRego.setVisibility(View.VISIBLE);
        if(capabilities != null && capabilities.isPromptOdometer())
            wcOdo.setVisibility(View.VISIBLE);
        doneBtn.setVisibility(View.VISIBLE);
        cancelBtn.setVisibility(View.VISIBLE);

        rcQuestionGroup.setVisibility(View.GONE);
        rcSuccessGroup.setVisibility(View.GONE);
    }

    private void showRewardCardLayout() {
        restartTimeOut();
        TextView receiptTextView = this.findViewById(R.id.receiptTextView);
        Button doneBtn = this.findViewById(R.id.doneBtn);
        Button cancelBtn = this.findViewById(R.id.cancelBtn);
        LinearLayout wcRego = this.findViewById(R.id.wc_vehicleRegistration);
        LinearLayout wcOdo = this.findViewById(R.id.wc_vehicleOdometer);
        View bankCard = this.findViewById(R.id.bankCard);
        ImageView logoImageView = this.findViewById(R.id.logoImageView);
        View rcQuestionGroup = this.findViewById(R.id.rcQuestionGroup);
        View rcSuccessGroup = this.findViewById(R.id.rcSuccessGroup);

        receiptTextView.setVisibility(View.GONE);
        logoImageView.setVisibility(View.GONE);
        wcRego.setVisibility(View.GONE);
        wcOdo.setVisibility(View.GONE);
        bankCard.setVisibility(View.GONE);
        doneBtn.setVisibility(View.GONE);
        cancelBtn.setVisibility(View.VISIBLE);
        rcQuestionGroup.setVisibility(View.VISIBLE);
        rcSuccessGroup.setVisibility(View.GONE);
    }

    private void showRewardCardSuccess() {
        restartTimeOut();
        TextView receiptTextView = this.findViewById(R.id.receiptTextView);
        Button doneBtn = this.findViewById(R.id.doneBtn);
        Button cancelBtn = this.findViewById(R.id.cancelBtn);
        LinearLayout wcRego = this.findViewById(R.id.wc_vehicleRegistration);
        LinearLayout wcOdo = this.findViewById(R.id.wc_vehicleOdometer);
        View bankCard = this.findViewById(R.id.bankCard);
        ImageView logoImageView = this.findViewById(R.id.logoImageView);
        View rcQuestionGroup = this.findViewById(R.id.rcQuestionGroup);
        View rcSuccessGroup = this.findViewById(R.id.rcSuccessGroup);

        receiptTextView.setVisibility(View.GONE);
        logoImageView.setVisibility(View.GONE);
        wcRego.setVisibility(View.GONE);
        wcOdo.setVisibility(View.GONE);
        bankCard.setVisibility(View.GONE);
        doneBtn.setVisibility(View.GONE);
        cancelBtn.setVisibility(View.GONE);
        rcQuestionGroup.setVisibility(View.GONE);
        rcSuccessGroup.setVisibility(View.VISIBLE);
    }
}
