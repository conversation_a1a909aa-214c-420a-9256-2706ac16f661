<!--<configuration
    xmlns="https://tony19.github.io/logback-android/xml"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="https://tony19.github.io/logback-android/xml https://cdn.jsdelivr.net/gh/tony19/logback-android/logback.xsd"
>
    <appender name="logcat" class="ch.qos.logback.classic.android.LogcatAppender">
        <tagEncoder>
            <pattern>%logger{12}</pattern>
        </tagEncoder>
        <encoder>
            <pattern>[%-20thread] %msg</pattern>
        </encoder>
    </appender>

    <root level="DEBUG">
        <appender-ref ref="logcat" />
    </root>
</configuration>-->
<configuration>
    <appender name="EMAIL" class="ch.qos.logback.classic.net.SMTPAppender">
        <!--<evaluator class="ch.qos.logback.classic.boolex.OnMarkerEvaluator">
            <marker>NOTIFY_ADMIN</marker>
        </evaluator>-->
        <cyclicBufferTracker>
            <!-- send 1 log entries per email -->
            <bufferSize>10</bufferSize>
        </cyclicBufferTracker>
        <smtpHost>smtp.office365.com</smtpHost>
        <smtpPort>587</smtpPort>
        <SSL>true</SSL>
        <username><EMAIL></username>
        <password>Peakhurst!234</password>
        <to><EMAIL></to>
        <from><EMAIL></from>
        <subject>%date{yyyyMMdd'T'HH:mm:ss.SSS}; %-5level; %msg</subject>
        <layout class="ch.qos.logback.classic.html.HTMLLayout" />
    </appender>

    <root level="ERROR">
        <appender-ref ref="EMAIL" />
    </root>
</configuration>