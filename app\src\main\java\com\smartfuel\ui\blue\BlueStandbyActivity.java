package com.smartfuel.ui.blue;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.IBinder;
import android.text.Html;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.cardview.widget.CardView;

import com.smartfuel.R;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.logger.Log;
import com.smartfuel.ui.BaseActivity;
import com.smartfuel.ui.StartupActivity;
import com.smartfuel.ui.utils.ImageDownloader;

import java.io.PrintWriter;
import java.io.StringWriter;

public class BlueStandbyActivity extends BaseActivity implements IServiceEvents {

    private static boolean isOpen = false;

    private String tintColor = "#1A588E";
    private String logoUrl = "";
    private String contact = "";

    private ImageDownloader imageDownloader;

    @Override
    public void onServiceConnected(ComponentName componentName, IBinder binder) {
        super.onServiceConnected(componentName, binder);
        try{
            getService().registerClient(this);
            initialize();
        }catch (Exception e){
            Log.e("BlueStandbyActivity", e);
        }
    }

    @Override
    public void onServiceDisconnected(ComponentName componentName) {
        startActivity(new Intent(this, StartupActivity.class));
        finish();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if(isOpen) {
            finish();
            return;
        }
        isOpen = true;

        connectToService(this);
    }

    private void initialize(){
        setContentView(R.layout.activity_blue_standby);

        imageDownloader = new ImageDownloader(this);

        tintColor = getService().getConfiguration("tint_color", "#1A588E");
        int tintColorInt = Color.parseColor(tintColor);

        ((CardView)findViewById(R.id.cardInfo)).setCardBackgroundColor(tintColorInt);

        logoUrl = getService().getConfiguration("logo_url", "");
        if(!logoUrl.isEmpty()) {
            ImageView logo = findViewById(R.id.logo);
            imageDownloader.loadImage(logoUrl, bitmap -> {
                if (bitmap != null) {
                    logo.setImageBitmap(bitmap);
                }
            });
        }

        String text = getService().getConfiguration("standby_text", "");
        ((TextView)findViewById(R.id.txtStandBy)).setText(Html.fromHtml(text, Html.FROM_HTML_MODE_LEGACY));
    }

    @Override
    public void systemError(String paramString, Throwable paramThrowable) {
        if(paramThrowable != null) {
            Log.e(paramString, paramThrowable);
            runOnUiThread(() -> {

                StringWriter stringWriter = new StringWriter();
                if(paramThrowable!=null) {
                    paramThrowable.printStackTrace(new PrintWriter(stringWriter));
                }
                else {
                    stringWriter.write("FAILED");
                }

                isOpen = false;

                Intent intent = new Intent(this, BlueErrorActivity.class);
                intent.putExtra(BlueErrorActivity.ERROR_MESSAGE, paramThrowable.getMessage());
                intent.putExtra(BlueErrorActivity.ERROR_DETAIL, stringWriter.toString());
                intent.putExtra(BlueErrorActivity.TINT_COLOR, tintColor);
                intent.putExtra(BlueErrorActivity.LOGO_URL, logoUrl);
                intent.putExtra(BlueErrorActivity.CONTACT, contact);
                startActivity(intent);
                finish();
            });
        }
    }

    @Override
    public void showHomeScreen() {
        isOpen = false;
        Log.i("BlueStandbyActivity", "Going Home");
        startActivity(new Intent((Context) this, BlueHomeActivity.class));
        finish();
    }
}