package com.smartfuel.ui.blue.adapters;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.smartfuel.R;
import com.smartfuel.service.models.forecourt.FuelPoint;
import com.smartfuel.ui.utils.DrawableUtil;

import java.util.ArrayList;

public class BlueFuelPointViewAdapter extends RecyclerView.Adapter<BlueFuelPointViewAdapter.ViewHolder> {
    private ItemClickListener mClickListener;

    private ArrayList<FuelPoint> mData;

    private LayoutInflater mInflater;

    private int tintColor;

    public BlueFuelPointViewAdapter(Context context, ArrayList<FuelPoint> fuelPointList, int tintColor) {
        this.mInflater = LayoutInflater.from(context);
        this.mData = fuelPointList;
        this.tintColor = tintColor;
    }

    public void updateData(ArrayList<FuelPoint> fuelPointList){
        this.mData = fuelPointList;
        notifyDataSetChanged();
    }

    public int getItemCount() {
        return this.mData.size();
    }

    public void onBindViewHolder(ViewHolder viewHolder, int index) {
        String str = this.mData.get(index).getState();
        index = Integer.parseInt(this.mData.get(index).getId());
        viewHolder.txtPumpNum.setText(String.valueOf(index));
        viewHolder.layBackground.setBackgroundColor(tintColor);
        if (str.equals("02H")) {
            viewHolder.txtInfo.setBackground(DrawableUtil.createButtonBackground(ContextCompat.getColor(mInflater.getContext(), R.color.blue_green), Color.TRANSPARENT, 20f, 0));
            viewHolder.txtInfo.setText("Select");
            viewHolder.txtInfo.setEnabled(true);
            viewHolder.txtInfo.setClickable(true);
        } else {
            viewHolder.txtInfo.setBackground(DrawableUtil.createButtonBackground(ContextCompat.getColor(mInflater.getContext(), R.color.blue_red), Color.TRANSPARENT, 20f, 0));
            viewHolder.txtInfo.setEnabled(false);
            viewHolder.txtInfo.setClickable(false);
            viewHolder.txtInfo.setText("In Use");
        }
    }

    public ViewHolder onCreateViewHolder(ViewGroup paramViewGroup, int paramInt) {
        return new ViewHolder(this.mInflater.inflate(R.layout.recyclerview_blue_pump, paramViewGroup, false));
    }

    public void setClickListener(ItemClickListener paramItemClickListener) {
        this.mClickListener = paramItemClickListener;
    }

    public interface ItemClickListener {
        void onItemClick(View param1View, int param1Int);
    }

    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        View layBackground;
        TextView txtPumpNum;
        TextView txtInfo;

        ViewHolder(View param1View) {
            super(param1View);
            this.layBackground = param1View.findViewById(R.id.layBackground);
            this.txtPumpNum = param1View.findViewById(R.id.txtPumpNum);
            this.txtInfo = param1View.findViewById(R.id.txtInfo);
            param1View.setOnClickListener(this);
        }

        public void onClick(View view) {
            if (BlueFuelPointViewAdapter.this.mClickListener != null && txtInfo.isEnabled())
                BlueFuelPointViewAdapter.this.mClickListener.onItemClick(view, getAdapterPosition());
        }
    }
}
