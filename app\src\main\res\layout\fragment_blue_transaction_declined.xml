<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="@dimen/card_radius"
            app:cardElevation="0dp"
            app:contentPadding="@dimen/padding_button5"
            android:layout_marginHorizontal="@dimen/activity_horizontal_margin4"
            app:cardBackgroundColor="@color/blue_red">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/logo"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/app_bar_logo"
                    android:adjustViewBounds="true"
                    android:src="@drawable/sflogo_dark"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="@dimen/margin_bottom3"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/gibson_bold"
                    android:gravity="center_horizontal"
                    android:textSize="@dimen/text_title1"
                    android:textColor="@color/White"
                    android:text="@string/error"
                    android:layout_marginBottom="@dimen/margin_bottom3"/>
                <TextView
                    android:id="@+id/txtBankResponse"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/gibson_regular"
                    android:gravity="center_horizontal"
                    android:textSize="@dimen/text_title1"
                    android:textColor="@color/White"
                    android:text="@string/error"
                    android:layout_marginBottom="@dimen/margin_bottom3"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/gibson_regular"
                    android:gravity="center_horizontal"
                    android:textSize="@dimen/text_title2"
                    android:textColor="@color/White"
                    android:text="@string/error_info" />
            </LinearLayout>

        </androidx.cardview.widget.CardView>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/layContact"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="@dimen/activity_horizontal_margin2"
        android:layout_marginBottom="@dimen/margin_bottom3"
        android:visibility="gone">

        <TextView
            android:id="@+id/txtContact"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/gibson_regular"
            android:gravity="center_horizontal"
            android:textSize="@dimen/text_size3"
            android:textColor="@color/blue_black_text"/>

    </LinearLayout>

    <com.smartfuel.ui.blue.components.BlueButton1
        android:id="@+id/btnCancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/cancel_transaction"
        android:textSize="@dimen/text_title2"
        android:paddingVertical="@dimen/padding_button2"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin1"
        android:layout_marginBottom="@dimen/margin_bottom1"/>

</LinearLayout>