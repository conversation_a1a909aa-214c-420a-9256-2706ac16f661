package com.smartfuel.ui;

import android.os.Bundle;

import com.smartfuel.R;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.view.View;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.logger.Log;
import com.smartfuel.service.models.forecourt.GradePrice;
import com.smartfuel.service.OPTService;
import com.smartfuel.service.models.kiosk.response.UserResponse;
import com.smartfuel.service.models.transaction.CardReceipt;
import com.smartfuel.service.models.transaction.WhiteCardReceipt;


import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;


public class HomeActivity extends BaseActivity implements IServiceEvents {
    FuelGradePriceViewAdapter fuelGradeListAdapter;

    private ServiceConnection mConnection = new ServiceConnection() {
        public void onServiceConnected(ComponentName componentName, IBinder binder) {
            OPTService.LocalBinder localBinder = (OPTService.LocalBinder) binder;
            HomeActivity.this.myOPTService = localBinder.getServiceInstance();

            try {
                HomeActivity.this.myOPTService.registerClient(HomeActivity.this);
                HomeActivity.this.setContentView(R.layout.activity_home);

                ArrayList<GradePrice> configuredGradePrices = null;
                HomeActivity.this.showPriceDisplayBoard();

            } catch (Exception e) {
                Log.e("HomeScreen",e);
                //myHandler.removeCallbacks(myRunnable);
                //startActivity(new Intent(HomeActivity.this,StartupActivity.class));
                systemError("Error to create OPTService", e);
            }
        }

        public void onServiceDisconnected(ComponentName componentName) {
            Log.e("HomeScreen",new Exception("onServiceDisconnected"));
            //myHandler.removeCallbacks(myRunnable);
            //startActivity(new Intent(HomeActivity.this,StartupActivity.class));
            systemError("Error to create OPTService", new Exception("onServiceDisconnected"));
        }

    };

    protected void showPriceDisplayBoard(){
       /* runOnUiThread(() -> {
            ArrayList<GradePrice> configuredGradePrices = null;
            try {
                //forecourt refactor
                configuredGradePrices = getService().getConfiguredGradePrices().getGradePrices();

                if (configuredGradePrices == null || configuredGradePrices.size() == 0) {
                    //throw new Exception("No Fuel Grades & Prices configured");
                } else {
                    RecyclerView recyclerView = HomeActivity.this.findViewById(R.id.fuelPriceList);
                    recyclerView.setLayoutManager(new GridLayoutManager(HomeActivity.this.getBaseContext(), 3));
                    FuelGradePriceViewAdapter fuelGradeListAdapter = new FuelGradePriceViewAdapter(HomeActivity.this.getBaseContext(), configuredGradePrices);
                    recyclerView.setAdapter(fuelGradeListAdapter);
                }
            } catch (Exception e) {
                getService().Error(e);
            }
        });*/
        if (myOPTService.getConfiguredGradePrices() == null || myOPTService.getConfiguredGradePrices().getGradePrices().size() == 0) {

        } else {
            runOnUiThread(() -> {
                RecyclerView recyclerView = (RecyclerView) HomeActivity.this.findViewById(R.id.fuelPriceList);
                recyclerView.setLayoutManager((RecyclerView.LayoutManager) new GridLayoutManager(HomeActivity.this.getBaseContext(), 3));
                HomeActivity.this.fuelGradeListAdapter = new FuelGradePriceViewAdapter(HomeActivity.this.getBaseContext(), myOPTService.getConfiguredGradePrices().getGradePrices());
                recyclerView.setAdapter(HomeActivity.this.fuelGradeListAdapter);
            });

        }
    }
    OPTService myOPTService;

    public void onClickScreen(View paramView) {
        startActivity(new Intent( this, PumpSelectActivity.class));
        finish();
    }

    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        bindService(new Intent(this, OPTService.class), this.mConnection, BIND_AUTO_CREATE);
    }

    public void onReceiptClick(View view) {
        startActivity(new Intent( this, PresentReceiptActivity.class));
        finish();
    }

    protected void onResume() {
        super.onResume();
    }

    @Override
    public void customerReceiptDataReady(List<WhiteCardReceipt> paramList) {

    }

    @Override
    public void customerCardReceiptDataReady(List<CardReceipt> cardReceipts) {

    }

    @Override
    public void serviceReady() {

    }

    public void systemError(String paramString, Throwable paramThrowable) {
        StringWriter stringWriter = new StringWriter();
        if(paramThrowable!=null) {
            paramThrowable.printStackTrace(new PrintWriter(stringWriter));
        }
        else {
            stringWriter.write("FAILED");
        }
        Intent intent = new Intent((Context)this, SystemErrorActivity.class);
        intent.putExtra("ErrorMessage", paramString);
        intent.putExtra("ErrorStackTrace", stringWriter.toString());
        startActivity(intent);
        finish();
    }
    @Override
    public void gradePriceUpdate(){
            /*Refresh the fuel price board*/
        /*if(HomeActivity.this.fuelGradeListAdapter != null) {
            RecyclerView recyclerView = (RecyclerView) HomeActivity.this.findViewById(R.id.fuelPriceList);
            HomeActivity.this.fuelGradeListAdapter = new FuelGradePriceViewAdapter(HomeActivity.this.getBaseContext(), myOPTService.getConfiguredGradePrices().getGradePrices());
            recyclerView.setAdapter(HomeActivity.this.fuelGradeListAdapter);
        }*/
        showPriceDisplayBoard();
    }
    @Override
    public void whiteCardUserRejected(UserResponse paramUserResponseResponse) {

    }

    @Override
    public void cardTransactionDeclined(String bankResponse, String cardMask) {

    }

    @Override
    public void cardTransactionInProgress(String pumpNo, long processAmount) {

    }
    @Override
    public void showStandbyScreen(){
        Intent standbyIntent = new Intent((Context)this, StandbyActivity.class);
        startActivity(standbyIntent);
        finish();
    }

}
