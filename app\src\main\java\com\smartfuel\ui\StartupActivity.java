package com.smartfuel.ui;


import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.StrictMode;

import androidx.appcompat.app.AlertDialog;

import com.smartfuel.R;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.OPTService;
import com.smartfuel.ui.blue.BlueHomeActivity;

public class StartupActivity extends BaseActivity implements IServiceEvents {

    private ServiceConnection mConnection = new ServiceConnection() {
        public void onServiceConnected(ComponentName componentName, IBinder binder) {
            // Cancel timeout since service connected successfully
            if (timeoutRunnable != null) {
                timeoutHandler.removeCallbacks(timeoutRunnable);
            }

            OPTService.LocalBinder localBinder = (OPTService.LocalBinder) binder;
            StartupActivity.this.myOPTService = localBinder.getServiceInstance();
            new Thread(() -> {
                try {
                    StartupActivity.this.myOPTService.registerClient(StartupActivity.this);

                    // Log all configuration for debugging
                    StartupActivity.this.logAllConfiguration();

                    // Check if we're in offline/demo mode
                    String offlineMode = StartupActivity.this.myOPTService.getConfiguration("offline_mode", "false");
                    String testMode = StartupActivity.this.myOPTService.getConfiguration("test_mode", "false");
                    String networkEnabled = StartupActivity.this.myOPTService.getConfiguration("network_enabled", "false");
                    String apiHost = StartupActivity.this.myOPTService.getConfiguration("api_host", "");
                    String apiPort = StartupActivity.this.myOPTService.getConfiguration("api_port", "");

                    // Log configuration for debugging
                    android.util.Log.d("StartupActivity", "Configuration - offline_mode: " + offlineMode +
                        ", test_mode: " + testMode + ", network_enabled: " + networkEnabled +
                        ", api_host: " + apiHost + ", api_port: " + apiPort);

                    if ("true".equals(offlineMode) || "true".equals(testMode)) {
                        // Skip network connection in offline/test mode
                        android.util.Log.d("StartupActivity", "Running in offline/test mode - skipping network connection");
                        StartupActivity.this.serviceReady();
                    } else if ("true".equals(networkEnabled)) {
                        // Try normal connection to local API
                        android.util.Log.d("StartupActivity", "Attempting to connect to OPT API at " + apiHost + ":" + apiPort);
                        StartupActivity.this.myOPTService.LogonForecourtController();
                    } else {
                        // Network not enabled, go to service ready
                        android.util.Log.d("StartupActivity", "Network not enabled - going to service ready");
                        StartupActivity.this.serviceReady();
                    }
                } catch (Exception e) {
                    // Log the error for debugging
                    android.util.Log.e("StartupActivity", "Connection failed: " + e.getMessage(), e);
                    // If connection fails, go to demo mode
                    runOnUiThread(() -> {
                        startActivity(new Intent(StartupActivity.this, DemoActivity.class));
                        finish();
                    });
                }
            }).start();
            //TODO: What happens when the forecourt controller logon fails?
            //There needs to be error handling in place to manage the exception.
            //For the Application - it should show an Out of Service screen if the OPT Service cannot start.

        }

        public void onServiceDisconnected(ComponentName componentName) {
            // Go to demo mode if service disconnects
            startActivity(new Intent(StartupActivity.this, DemoActivity.class));
            finish();
        }
    };

    OPTService myOPTService;
    private Handler timeoutHandler = new Handler();
    private Runnable timeoutRunnable;

    protected void onCreate(Bundle paramBundle) {
        super.onCreate(paramBundle);

        StrictMode.ThreadPolicy tp = StrictMode.ThreadPolicy.LAX;
        StrictMode.setThreadPolicy(tp);

        setContentView(R.layout.activity_startup);

        try {
            Intent intent = new Intent((Context) this, OPTService.class);
            startService(intent);
            bindService(intent, this.mConnection, BIND_AUTO_CREATE);

            // Set a timeout for service connection
            timeoutRunnable = () -> {
                // If service doesn't connect within 10 seconds, go to demo mode
                startActivity(new Intent(StartupActivity.this, DemoActivity.class));
                finish();
            };
            timeoutHandler.postDelayed(timeoutRunnable, 10000); // 10 second timeout

        } catch (Exception e) {
            // If service binding fails, go to demo mode
            startActivity(new Intent(this, DemoActivity.class));
            finish();
        }
    }

    public void serviceReady() {
        try {
            String layoutType = myOPTService.getConfiguration("layout_type", "default");
            String demoMode = myOPTService.getConfiguration("demo_mode", "false");

            android.util.Log.d("StartupActivity", "serviceReady() - layoutType: " + layoutType + ", demoMode: " + demoMode);

            if ("true".equals(demoMode)) {
                // Use demo activity when in demo mode
                android.util.Log.d("StartupActivity", "Starting DemoActivity due to demo_mode=true");
                startActivity(new Intent(this, DemoActivity.class));
            } else if(layoutType.equals("default")) {
                android.util.Log.d("StartupActivity", "Starting HomeActivity with default layout");
                startActivity(new Intent(this, HomeActivity.class));
            } else if (layoutType.equals("blue")) {
                android.util.Log.d("StartupActivity", "Starting BlueHomeActivity with blue layout");
                startActivity(new Intent(this, BlueHomeActivity.class));
            }
        } catch (Exception e) {
            // If service is not available, go to demo mode
            android.util.Log.e("StartupActivity", "serviceReady() failed: " + e.getMessage(), e);
            startActivity(new Intent(this, DemoActivity.class));
        }
        finish();
    }

    // Method to test configuration loading
    public void logAllConfiguration() {
        try {
            android.util.Log.d("StartupActivity", "=== Configuration Debug ===");
            android.util.Log.d("StartupActivity", "offline_mode: " + myOPTService.getConfiguration("offline_mode", "NOT_SET"));
            android.util.Log.d("StartupActivity", "test_mode: " + myOPTService.getConfiguration("test_mode", "NOT_SET"));
            android.util.Log.d("StartupActivity", "demo_mode: " + myOPTService.getConfiguration("demo_mode", "NOT_SET"));
            android.util.Log.d("StartupActivity", "network_enabled: " + myOPTService.getConfiguration("network_enabled", "NOT_SET"));
            android.util.Log.d("StartupActivity", "api_host: " + myOPTService.getConfiguration("api_host", "NOT_SET"));
            android.util.Log.d("StartupActivity", "api_port: " + myOPTService.getConfiguration("api_port", "NOT_SET"));
            android.util.Log.d("StartupActivity", "api_protocol: " + myOPTService.getConfiguration("api_protocol", "NOT_SET"));
            android.util.Log.d("StartupActivity", "layout_type: " + myOPTService.getConfiguration("layout_type", "NOT_SET"));
            android.util.Log.d("StartupActivity", "=== End Configuration Debug ===");
        } catch (Exception e) {
            android.util.Log.e("StartupActivity", "Failed to log configuration: " + e.getMessage(), e);
        }
    }

    public void systemError(String paramString, Throwable paramThrowable) {
        runOnUiThread(() -> {
            Handler tryAgainHandler = new Handler(Looper.myLooper());

            AlertDialog.Builder alertDialogBuilder = new AlertDialog.Builder(this);
            alertDialogBuilder.setTitle(paramString);
            alertDialogBuilder.setMessage(paramThrowable.getMessage()).setCancelable(false);
            alertDialogBuilder.setPositiveButton("Try again", (dialog, which) -> {
                tryAgainHandler.removeCallbacksAndMessages(null);
                connectToService(mConnection);
            });
            AlertDialog alertDialog = alertDialogBuilder.create();
            alertDialog.show();

            Runnable tryAgain = () -> {
                alertDialog.getButton(AlertDialog.BUTTON_POSITIVE).performClick();
            };
            tryAgainHandler.postDelayed(tryAgain, RESTART_COUNT_DELAY);
        });
    }
}