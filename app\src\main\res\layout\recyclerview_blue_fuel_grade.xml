<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/activity_horizontal_half_margin2"
    app:cardCornerRadius="@dimen/card_radius2"
    app:cardElevation="0dp">

    <LinearLayout
        android:id="@+id/layBackground"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:minWidth="@dimen/min_width"
        android:gravity="center"
        android:orientation="vertical"
        android:background="@color/Black"
        android:padding="@dimen/padding_button1">

        <TextView
            android:id="@+id/fuelName"
            android:fontFamily="@font/gibson_bold"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/fuel_name"
            android:textSize="@dimen/text_size5"
            android:textColor="@android:color/white" />
    </LinearLayout>

</androidx.cardview.widget.CardView>