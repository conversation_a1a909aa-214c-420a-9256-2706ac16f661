<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginHorizontal="@dimen/activity_horizontal_half_margin2">

        <TextView
            android:id="@+id/txtSelectPump"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/gibson_bolditalic"
            android:gravity="center_horizontal"
            android:textSize="@dimen/text_title1"
            android:textColor="@color/Black"
            android:text="@string/select_pump_number"
            android:layout_marginBottom="@dimen/margin_bottom2"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/gibson_regular"
            android:gravity="center_horizontal"
            android:textSize="@dimen/text_title3"
            android:textColor="@color/blue_black_text"
            android:text="@string/select_pump_info"
            android:layout_marginBottom="@dimen/margin_bottom2"
            android:layout_marginHorizontal="@dimen/activity_horizontal_margin4"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@id/FuelPoints"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"/>
    </LinearLayout>

</LinearLayout>