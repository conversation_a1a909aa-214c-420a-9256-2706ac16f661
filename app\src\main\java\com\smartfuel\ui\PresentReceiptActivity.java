package com.smartfuel.ui;

import android.content.ComponentName;
import android.os.Bundle;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.IBinder;
import android.text.InputType;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;

import androidx.appcompat.app.AlertDialog;

import com.smartfuel.R;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.models.transaction.CardReceipt;

import org.apache.commons.validator.routines.EmailValidator;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.List;

public class PresentReceiptActivity extends BaseActivity implements IServiceEvents {
    String cardType = "CARD";
    String userQRCode = "";

    protected Handler myHandler;

    protected Runnable myRunnable;

    private List<Integer> TransactionNumberDetail;
    @Override
    public void onServiceConnected(ComponentName componentName, IBinder binder) {
        super.onServiceConnected(componentName, binder);
        try {
            getService().registerClient(this);
        } catch (Exception e) {
            systemError("Receipts Unavailable",e);
        }
        initialize();
    }

    public void onPrintReceiptClick(View paraView){
        this.myHandler.removeCallbacks(this.myRunnable);
        Intent intent = new Intent((Context)this, ReceiptProcessActivity.class);
        this.cardType = "CARD";
        intent.putExtra("TransactionNumberDetail", this.TransactionNumberDetail.toArray());
        intent.putExtra("CardType", this.cardType);
        startActivity(intent);
        finish();
    }
    public void onEmailReceiptClick(View paraView){
        AlertDialog.Builder alert = new AlertDialog.Builder(this);

        alert.setMessage("Enter your email");
        alert.setCancelable(false);

        final EditText email = new EditText(this);
        email.setInputType(InputType.TYPE_CLASS_TEXT
                | InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS);
        email.setHint("Email...");

        alert.setView(email);
        alert.setPositiveButton("Ok", null);
        alert.setNegativeButton("Cancel", (dialog, which) -> {
            serviceReady();
        });

        AlertDialog dialog = alert.create();
        dialog.setOnShowListener(dialogInterface -> {

            Button button = ((AlertDialog) dialog).getButton(AlertDialog.BUTTON_POSITIVE);
            button.setOnClickListener((v) -> {
                String text = email.getText().toString().trim();
                if(!text.isEmpty() && EmailValidator.getInstance()
                        .isValid(text)) {
                    try {
                        getService().PrepareCustomerReceipt(this.TransactionNumberDetail,cardType);
                        getService().sendTransactionReceipt(null, text);
                        serviceReady();
                    } catch (Exception e) {
                        systemError("sendTransactionReceipt", e);
                    }
                    dialog.dismiss();
                }
            });
        });

        dialog.show();
    }
    public void onCancelClick(View paramView) throws InterruptedException {

            // Cancel the terminal waiting card read screen.
        getService().cancelCardTransaction();
        startActivity(new Intent(this, HomeActivity.class));
        finish();
    }

    protected void onCreate(Bundle paramBundle) {
        super.onCreate(paramBundle);

        connectToService(this);

        //setContentView(R.layout.activity_present_receipt);
        Handler handler = new Handler();
        this.myHandler = handler;
        Runnable runnable = ()-> {
                Intent intent = new Intent(PresentReceiptActivity.this, HomeActivity.class);
                PresentReceiptActivity.this.startActivity(intent);
                PresentReceiptActivity.this.finish();
        };
        this.myRunnable = runnable;
        handler.postDelayed(runnable, 15000L);
    }
    private void initialize(){
        setContentView(R.layout.activity_present_receipt);

        View btnBankCard = this.findViewById(R.id.btnBankCard);
        btnBankCard.setOnClickListener(v -> {
            myHandler.removeCallbacks(myRunnable);
            try {
                getService().initialiseReceipt();
            } catch (Exception e) {
                systemError("No Receipt Data", e);
            }
        });

        btnBankCard.performClick();
    }
    public void onDebugClick(View paramView) {
        /*this.userQRCode = "{\"user\":\"b91aaf7d-dfc0-4866-97b7-e039d240fec7\",\"vehicleRegistration\":\"Yrf-18g\",\"vehicleOdometer\":\"555231\"}";
        Intent intent = new Intent((Context)this, ReceiptProcessActivity.class);
        this.cardType = "WHITECARD";
        intent.putExtra("QRCodeData", this.userQRCode);
        intent.putExtra("cardType", this.cardType);
        startActivity(intent);
        finish();*/
    }

    @Override
    public void cardReceiptComplete(List<Integer> terminalTransactionId){
        runOnUiThread(() -> {
            this.TransactionNumberDetail = terminalTransactionId;//Integer.parseInt(cardReceipt.TransactionNumberDetail);

            View btnPrintReceipt = this.findViewById(R.id.btnPrintReceipt);
            btnPrintReceipt.setVisibility(View.VISIBLE);

            View btnEmailReceipt = this.findViewById(R.id.btnEmailReceipt);
            btnEmailReceipt.setVisibility(View.VISIBLE);



        });
    }

    public void serviceReady() {
        Intent intent = new Intent(this, HomeActivity.class);
        startActivity(intent);
        finish();
    }
    @Override
    public void systemError(String paramString, Throwable paramThrowable) {
        StringWriter stringWriter = new StringWriter();
        if(paramThrowable !=null) {
            paramThrowable.printStackTrace(new PrintWriter(stringWriter));
        }
        Intent intent = new Intent((Context) this, SystemErrorActivity.class);
        intent.putExtra("ErrorMessage", paramString);
        intent.putExtra("ErrorStackTrace", stringWriter.toString());
        startActivity(intent);
        finish();
    }

    public boolean onKeyDown(int paramInt, KeyEvent paramKeyEvent) {
        char c = (char)paramKeyEvent.getUnicodeChar();
        if (c == '|') {
            this.myHandler.removeCallbacks(this.myRunnable);
            Intent intent = new Intent((Context)this, ReceiptProcessActivity.class);
            this.cardType = "WHITECARD";
            intent.putExtra("QRCodeData", this.userQRCode);
            intent.putExtra("CardType", this.cardType);
            startActivity(intent);
            finish();
        } else if (paramInt != 59) {
            this.userQRCode += c;
        }
        return true;
    }

    public boolean onKeyUp(int paramInt, KeyEvent paramKeyEvent) {
        return true;
    }
}
