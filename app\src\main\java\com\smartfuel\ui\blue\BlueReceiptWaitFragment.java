package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.fragment.app.Fragment;

import com.smartfuel.R;

public class BlueReceiptWaitFragment extends Fragment {

    private final int tintColor;
    private boolean isPrint;

    public BlueReceiptWaitFragment(int tintColor) {
        this.tintColor = tintColor;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_receipt_wait, container, false);

        TextView title = view.findViewById(R.id.txtTitle);
        title.setTextColor(tintColor);
        if(isPrint)
            title.setText("Print receipt");
        else
            title.setText("Email receipt");

        ((CardView)view.findViewById(R.id.cardInfo)).setCardBackgroundColor(tintColor);

        return view;
    }

    public void setIsPrint(boolean isPrint){
        this.isPrint = isPrint;
    }
}