<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_INTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <uses-feature android:name="android.hardware.usb.host" />
    <uses-feature android:name="android.hardware.usb.accessory" />

    <application
        android:name=".AppContext"
        android:allowBackup="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Kiosk"
        android:usesCleartextTraffic="true"
        tools:targetApi="28">
        <activity
            android:name=".ui.blue.BlueReceiptActivity"
            android:exported="false"
            android:theme="@style/Theme.Kiosk.Fullscreen" />
        <activity
            android:name=".ui.blue.BlueTransactionActivity"
            android:exported="false"
            android:theme="@style/Theme.Kiosk.Fullscreen" />
        <activity
            android:name=".ui.blue.BluePromoActivity"
            android:exported="false"
            android:theme="@style/Theme.Kiosk.Fullscreen" />
        <activity
            android:name=".ui.blue.BlueHomeActivity"
            android:exported="false"
            android:theme="@style/Theme.Kiosk.Fullscreen" />
        <activity
            android:name=".ui.blue.BlueErrorActivity"
            android:exported="false"
            android:theme="@style/Theme.Kiosk.Fullscreen" />
        <activity
            android:name=".ui.blue.BlueStandbyActivity"
            android:exported="false"
            android:theme="@style/Theme.Kiosk.Fullscreen" />
        <activity
            android:name=".ui.StandbyActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:label="@string/title_activity_standby"
            android:theme="@style/Theme.Kiosk.Fullscreen" />
        <activity
            android:name=".ui.TransactionDeclinedActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:label="@string/title_activity_transaction_declined"
            android:theme="@style/Theme.Kiosk.Fullscreen" />

        <activity
            android:name=".ui.DemoActivity"
            android:exported="false"
            android:theme="@style/Theme.Kiosk.Fullscreen" />

        <service
            android:name="com.smartfuel.service.OPTService"
            android:exported="true">
            <intent-filter>
                <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
            </intent-filter>
        </service>

        <activity
            android:name=".ui.TransactionSucessActivity"
            android:exported="false"
            android:theme="@style/Theme.Kiosk.Fullscreen" />
        <activity
            android:name=".ui.TransactionProcessActivity"
            android:exported="false"
            android:theme="@style/Theme.Kiosk.Fullscreen" />
        <activity
            android:name=".ui.SystemErrorActivity"
            android:exported="false"
            android:theme="@style/Theme.Kiosk.Fullscreen" />
        <activity
            android:name=".ui.ReceiptProcessActivity"
            android:exported="false"
            android:theme="@style/SplashTheme" />
        <activity
            android:name=".ui.PumpSelectActivity"
            android:exported="false"
            android:theme="@style/Theme.Kiosk.Fullscreen" />
        <activity
            android:name=".ui.PresentReceiptActivity"
            android:exported="false"
            android:theme="@style/Theme.Kiosk.Fullscreen" />
        <activity
            android:name=".ui.PresentPaymentActivity"
            android:exported="false"
            android:theme="@style/Theme.Kiosk.Fullscreen" />
        <activity
            android:name=".ui.HomeActivity"
            android:exported="false"
            android:theme="@style/Theme.Kiosk.Fullscreen" />
        <activity
            android:name=".ui.StartupActivity"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <!--category android:name="android.intent.category.HOME" />
                <category android:name="android.intent.category.DEFAULT" /-->
            </intent-filter>
        </activity>
    </application>

</manifest>