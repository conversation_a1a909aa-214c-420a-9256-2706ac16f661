package com.smartfuel;

import android.app.AlarmManager;
import android.app.Application;
import android.app.smdt.SmdtManager;
import android.content.Context;
import android.os.StrictMode;

import com.google.firebase.crashlytics.CustomKeysAndValues;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.smartfuel.service.PropertyReader;
import com.smartfuel.service.logger.Log;
import com.smartfuel.service.logger.LoggerService;
import com.smartfuel.service.property.IProperties;

public class AppContext extends Application {

    private SmdtManager smdt;
    public AppContext() {
        if (BuildConfig.DEBUG)
            StrictMode.enableDefaults();
    }

    public void onCreate() {
        super.onCreate();

        //Thread.setDefaultUncaughtExceptionHandler(_unCaughtExceptionHandler);

        com.smartfuel.PropertyReader.configureParam();

        try {
            smdt = SmdtManager.create(this);
            if (smdt != null) {
                smdt.setGestureBar(false);
                smdt.smdtSetStatusBar(this, false);
            }
            /*Setup the User for Analytics reporting*/
            Thread analyticsThread = new Thread() {
                @Override
                public void run() {
                    com.smartfuel.service.PropertyReader propertyReader = new PropertyReader(getBaseContext());
                    IProperties properties = propertyReader.getMyProperties();
                    FirebaseCrashlytics.getInstance().setUserId(properties.getAndroidId(AppContext.this));

                    CustomKeysAndValues keysAndValues = new CustomKeysAndValues.Builder()
                            .putString("DeviceId", properties.getAndroidId(AppContext.this))
                            .putString("VersionId", BuildConfig.VERSION_NAME)
                            .build();

                    FirebaseCrashlytics.getInstance().setCustomKeys(keysAndValues);
                }
            };
            analyticsThread.start();

            LoggerService.setAppVersion(BuildConfig.VERSION_NAME);
        }
        catch (Exception e){
            Log.d("AppStartup",e.getMessage());
        }

    }

    private Thread.UncaughtExceptionHandler _unCaughtExceptionHandler = new Thread.UncaughtExceptionHandler() {
        @Override
        public void uncaughtException(Thread thread, Throwable ex) {
            ex.printStackTrace();
            Log.e("UncaughtExceptionHandler", ex);
        }
    };
}
