package com.smartfuel;

import android.content.Context;
import android.provider.Settings;

import com.google.android.gms.ads.identifier.AdvertisingIdClient;
import com.google.android.gms.common.GooglePlayServicesNotAvailableException;
import com.google.android.gms.common.GooglePlayServicesRepairableException;
import com.smartfuel.service.property.IProperties;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

public class PropertyReader {
    public static void configureParam(){
        com.smartfuel.service.PropertyReader.setPropertiesHandle(new com.smartfuel.service.PropertyReader.IPropertiesHandle() {
            @Override
            public IProperties getHandle() {
                return new KioskProperties();
            }

            @Override
            public IProperties loadProperties(Context context, IProperties properties, String paramString) throws IOException {
                if(!paramString.equals("")) {
                    InputStream inputStream = context.getAssets().open(paramString);
                    properties.load(inputStream);
                }
                return properties;
            }
        });
    }

    public static class KioskProperties extends Properties implements IProperties {

        public static String androidId = null;

        public KioskProperties() {
            super();
        }

        @Override
        public String getAndroidId(Context context) {
            if(androidId != null && !androidId.isEmpty())
                return androidId;

            AdvertisingIdClient.Info adInfo = null;
            try {
                adInfo = AdvertisingIdClient.getAdvertisingIdInfo(context);

            } catch (IOException | GooglePlayServicesNotAvailableException |
                     GooglePlayServicesRepairableException exception) {
                return Settings.Secure.getString(context.getContentResolver(), "android_id");
            }
            androidId =  adInfo.getId();
            return androidId;
        }
    }
}
