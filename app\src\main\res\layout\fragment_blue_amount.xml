<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal">


    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_bolditalic"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title1"
        android:textColor="@color/Black"
        android:text="@string/enter_amount"
        android:layout_marginBottom="@dimen/margin_bottom1"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal">

        <fragment
            android:id="@+id/fragment_num_keyboard"
            android:name="com.smartfuel.ui.blue.BlueNumKeyboardFragment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>

        <com.smartfuel.ui.blue.components.BlueButton1
            android:id="@+id/btnQuickFuel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_title3"
            android:layout_marginTop="@dimen/activity_vertical_margin1"
            android:paddingVertical="@dimen/padding_button3"
            app:layout_constraintTop_toBottomOf="@id/fragment_num_keyboard"
            app:layout_constraintLeft_toLeftOf="@id/fragment_num_keyboard"
            app:layout_constraintRight_toRightOf="@id/fragment_num_keyboard" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"/>

    <com.smartfuel.ui.blue.components.BlueButton1
        android:id="@+id/btnNext"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/next"
        android:textSize="@dimen/text_title2"
        android:paddingVertical="@dimen/padding_button2"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin1"
        android:layout_marginBottom="@dimen/margin_bottom1"/>

</LinearLayout>