package com.smartfuel.ui.blue.components;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;

import com.smartfuel.R;

public class BlueButton3 extends BaseBlueButton {

    private Integer color = null;

    @Override
    protected int getButtonBackgroundColor() {
        if(color == null) {
            Drawable background = getBackground();
            if (background instanceof ColorDrawable) {
                ColorDrawable colorDrawable = (ColorDrawable) background;

                color = colorDrawable.getColor();
            }else if (background instanceof GradientDrawable) {
                GradientDrawable colorDrawable = (GradientDrawable) background;

                color = colorDrawable.getColor().getDefaultColor();
            } else {
                color = Color.TRANSPARENT;
            }
        }
        return color;
    }

    @Override
    protected int getButtonBorderColor() {
        return ContextCompat.getColor(getContext(), R.color.blue_black_text);
    }

    @Override
    protected int getButtonTextColor() {
        return ContextCompat.getColor(getContext(), R.color.blue_black_text);
    }

    @Override
    protected float getCornerRadius(){
        return 0f;
    }

    public BlueButton3(@NonNull Context context) {
        super(context);
        Typeface typeface = ResourcesCompat.getFont(getContext(), R.font.gibson_regular);
        setTypeface(typeface);
    }

    public BlueButton3(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        Typeface typeface = ResourcesCompat.getFont(getContext(), R.font.gibson_regular);
        setTypeface(typeface);
    }

    public BlueButton3(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        Typeface typeface = ResourcesCompat.getFont(getContext(), R.font.gibson_regular);
        setTypeface(typeface);
    }
}
