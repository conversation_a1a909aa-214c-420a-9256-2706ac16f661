package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.smartfuel.R;
import com.smartfuel.ui.BaseActivity;
import com.smartfuel.ui.blue.components.NoScrollWebView;

public class BluePromoActivity extends BaseActivity {

    public static final String PARAM_PROMO_URL = "PARAM_PROMO_URL";

    private NoScrollWebView webView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        String url = getIntent().getStringExtra(PARAM_PROMO_URL);

        setContentView(R.layout.activity_blue_promo);

        webView = findViewById(R.id.webView);
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                return false;
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                view.loadUrl("javascript:(function() { " +
                        "var anchors = document.getElementsByTagName('a'); " +
                        "for (var i = 0; i < anchors.length; i++) { " +
                        "anchors[i].removeAttribute('href'); " +
                        "anchors[i].onclick = function() { return false; } " +
                        "} " +
                        "})()");
            }
        });

        /*webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public boolean onConsoleMessage(android.webkit.ConsoleMessage cm) {
                Log.d("WebView", cm.message() + " -- From line "
                        + cm.lineNumber() + " of "
                        + cm.sourceId());
                return true;
            }
        });*/

        webView.getSettings().setJavaScriptEnabled(true);
        webView.loadUrl(url);

        findViewById(R.id.layScreen).setOnTouchListener((v, event) -> {
            finish();
            return true;
        });
    }
}