package com.smartfuel.ui.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.AsyncTask;

import com.smartfuel.service.logger.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

public class ImageDownloader {

    private Context context;

    public ImageDownloader(Context context) {
        this.context = context;
    }

    public void loadImage(String imageUrl, ImageDownloadCallback callback) {
        String imageName = imageUrl.substring(imageUrl.lastIndexOf('/')+1);
        File imageFile = new File(context.getFilesDir(), imageName);

        if (imageFile.exists()) {
            Bitmap bitmap = BitmapFactory.decodeFile(imageFile.getAbsolutePath());
            callback.onImageDownloaded(bitmap);
        } else {
            new DownloadImageTask(imageUrl, imageName, callback).execute();
        }
    }

    private class DownloadImageTask extends AsyncTask<Void, Void, Bitmap> {
        private String imageUrl;
        private String imageName;
        private ImageDownloadCallback callback;

        public DownloadImageTask(String imageUrl, String imageName, ImageDownloadCallback callback) {
            this.imageUrl = imageUrl;
            this.imageName = imageName;
            this.callback = callback;
        }

        @Override
        protected Bitmap doInBackground(Void... voids) {
            Bitmap bitmap = null;
            try {
                URL url = new URL(imageUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.connect();

                InputStream input = connection.getInputStream();
                bitmap = BitmapFactory.decodeStream(input);
                input.close();

                File imageFile = new File(context.getFilesDir(), imageName);
                FileOutputStream outputStream = new FileOutputStream(imageFile);
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream);
                outputStream.flush();
                outputStream.close();
            } catch (Exception e) {
                Log.e("ImageDownloader", e);
            }
            return bitmap;
        }

        @Override
        protected void onPostExecute(Bitmap bitmap) {
            if (callback != null) {
                callback.onImageDownloaded(bitmap);
            }
        }
    }

    public interface ImageDownloadCallback {
        void onImageDownloaded(Bitmap bitmap);
    }
}
