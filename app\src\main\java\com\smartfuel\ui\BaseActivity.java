package com.smartfuel.ui;

import android.content.ComponentName;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import androidx.annotation.CallSuper;
import androidx.appcompat.app.AppCompatActivity;

import com.smartfuel.service.OPTService;
import com.smartfuel.ui.blue.BluePromoActivity;

public abstract class BaseActivity extends AppCompatActivity implements ServiceConnection {
    private View mDecorView;
    private OPTService myOPTService;
    protected final int RESTART_COUNT_DELAY = 30000; // default restart timeout value used on any activity
    protected OPTService getService(){
        return myOPTService;
    }

    private Handler myHandler;
    private Runnable myRunnable;

    protected void removeTimer(){
        if(myHandler != null)
            myHandler.removeCallbacks(myRunnable);
    }

    protected void setupTimer(long timeOut, Runnable action){
        this.myHandler = new Handler();
        this.myRunnable = () -> {
            myHandler.removeCallbacks(null);
            action.run();
        };
        startTimer(timeOut);
    }

    protected void startTimer(long timeOut){
        removeTimer();
        if(myHandler != null)
            myHandler.postDelayed(myRunnable, timeOut);
    }

    protected void connectToService(ServiceConnection conn){
        Intent intent = new Intent(this, OPTService.class);
        startService(intent);
        bindService(intent, conn, BIND_AUTO_CREATE);
    }
    protected void disconnectFromService(){
        getService().stopSelf();
        unbindService(this);
    }

    @CallSuper
    @Override
    public void onServiceConnected(ComponentName componentName, IBinder binder) {
        OPTService.LocalBinder localBinder = (OPTService.LocalBinder)binder;
        myOPTService = localBinder.getServiceInstance();
    }

    @Override
    public void onServiceDisconnected(ComponentName componentName) {
    }

    private void hideSystemUI() {
        final int flags = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                | View.SYSTEM_UI_FLAG_LOW_PROFILE;

        this.mDecorView.setSystemUiVisibility(flags); //6022
    }

    protected void onCreate(Bundle paramBundle) {
        super.onCreate(paramBundle);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
    }
    @Override
    protected void onPostCreate(Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);

        mDecorView = ((ViewGroup)getWindow().getDecorView()).getChildAt(0);
        hideSystemUI();
    }
    protected void onResume() {
        super.onResume();
        hideSystemUI();
    }
    protected void onDestroy() {

        if(myOPTService != null)
            unbindService(this);
        super.onDestroy();
    }
}
