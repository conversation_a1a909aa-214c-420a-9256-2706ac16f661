package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.os.Handler;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.blue.components.BlueButton1;
import com.smartfuel.ui.utils.ImageDownloader;

public class BlueReceiptDeclinedFragment extends Fragment {

    private final int tintColor;
    private final String contact;

    private String error;
    private boolean repeat = true;
    private boolean isPrint;

    private final DeclinedListener listener;

    private Handler myHandler;
    private Runnable myRunnable;

    long timeOut = 10000L;

    public BlueReceiptDeclinedFragment(int tintColor, String contact, DeclinedListener listener) {
        this.tintColor = tintColor;
        this.contact = contact;
        this.listener = listener;
    }

    protected void removeTimer(){
        if(myHandler != null)
            myHandler.removeCallbacks(myRunnable);
    }

    protected void setupTimer(long timeOut, Runnable action){
        this.myHandler = new Handler();
        this.myRunnable = () -> {
            myHandler.removeCallbacks(null);
            action.run();
        };
        startTimer(timeOut);
    }

    protected void startTimer(long timeOut){
        removeTimer();
        if(myHandler != null)
            myHandler.postDelayed(myRunnable, timeOut);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_receipt_declined, container, false);


        TextView title = view.findViewById(R.id.txtTitle);
        title.setTextColor(tintColor);
        if(isPrint)
            title.setText("Print receipt");
        else
            title.setText("Email receipt");

        BlueButton1 btnClose = view.findViewById(R.id.btnClose);
        btnClose.setBackgroundColor(tintColor);
        btnClose.setOnClickListener(v -> {
            removeTimer();
            listener.onCancelClick();
        });

        ((TextView)view.findViewById(R.id.txtError)).setText(error);

        if(!contact.isEmpty()){
            view.findViewById(R.id.layContact).setVisibility(View.VISIBLE);
            ((TextView)view.findViewById(R.id.txtContact)).setText(Html.fromHtml(contact, Html.FROM_HTML_MODE_LEGACY));
        }

        setupTimer(timeOut, () -> listener.onTimeOut(repeat));

        return view;
    }

    public void setError(String error){
        this.error = error;
    }

    public void setRepeat(boolean repeat){
        this.repeat = repeat;
    }

    public void setIsPrint(boolean isPrint){
        this.isPrint = isPrint;
    }

    public interface DeclinedListener{
        void onTimeOut(boolean repeat);
        void onCancelClick();
    }
}