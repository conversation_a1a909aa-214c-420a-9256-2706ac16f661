package com.smartfuel.ui.blue;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.text.Html;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import com.smartfuel.R;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.logger.Log;
import com.smartfuel.ui.BaseActivity;
import com.smartfuel.ui.StandbyActivity;
import com.smartfuel.ui.StartupActivity;
import com.smartfuel.ui.SystemErrorActivity;
import com.smartfuel.ui.blue.components.BlueButton2;

import java.io.PrintWriter;
import java.io.StringWriter;

public class BlueHomeActivity extends BaseActivity implements IServiceEvents {

    long delayPromo = 15000L;

    private String tintColor = "#1A588E";
    private String logoUrl = "";
    private String contact = "";

    BlueAppBarFragment appBarFragment;

    private void setupPromo(String promoURL){
        setupTimer(delayPromo + 15000L, () -> {
            openPromo(promoURL);
        });
    }

    private void openPromo(String promoURL){
        Intent intent = new Intent(this, BluePromoActivity.class);
        intent.putExtra(BluePromoActivity.PARAM_PROMO_URL, promoURL);
        this.startActivity(intent);
    }

    @Override
    public void onServiceConnected(ComponentName componentName, IBinder binder) {
        super.onServiceConnected(componentName, binder);
        try{
            getService().registerClient(this);
            initialize();
        }catch (Exception e){
            Log.e("BlueHomeScreen", e);
            removeTimer();
        }
    }

    @Override
    public void onServiceDisconnected(ComponentName componentName) {
        removeTimer();
        startActivity(new Intent(this, StartupActivity.class));
        finish();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        connectToService(this);
    }

    protected void onResume() {
        super.onResume();
        startTimer(delayPromo);
    }

    private void initialize(){
        setContentView(R.layout.activity_blue_home);

        appBarFragment = (BlueAppBarFragment) getSupportFragmentManager()
                .findFragmentById(R.id.fragment_custom_app_bar);

        tintColor = getService().getConfiguration("tint_color", "#1A588E");
        int tintColorInt = Color.parseColor(tintColor);

        if (appBarFragment != null) {
            appBarFragment.showCancel(false);
            appBarFragment.showBack(false);
            appBarFragment.setTintColor(tintColor);

            logoUrl = getService().getConfiguration("logo_url", "");
            appBarFragment.setLogo(logoUrl);
        }

        findViewById(R.id.vwStart).setBackgroundColor(tintColorInt);
        ((TextView)findViewById(R.id.txtReceipt)).setTextColor(tintColorInt);
        BlueButton2 btnStart = findViewById(R.id.btnStart);
        btnStart.setTintColor(tintColorInt);
        BlueButton2 btnReceipt = findViewById(R.id.btnReceipt);
        btnReceipt.setTintColor(tintColorInt);

        btnStart.setOnClickListener(v -> startTransaction());
        btnReceipt.setOnClickListener(v -> startReceipt());

        String store = getService().getConfiguration("store_name", "");
        ((TextView)findViewById(R.id.txtStore)).setText(store);

        String promo = getService().getConfiguration("promo_url", "");
        if(!promo.isEmpty()){
            String promoType = getService().getConfiguration("promo_type", "screensaver");
            if(promoType.equals("screensaver"))
                setupPromo(promo);
            else{
                appBarFragment.showPromo(true);
                appBarFragment.setPromoClickListener(v -> {
                    openPromo(promo);
                });
            }
        }

        contact = getService().getConfiguration("contact_text", "");
        if(!contact.isEmpty()){
            findViewById(R.id.layContact).setVisibility(View.VISIBLE);
            ((TextView)findViewById(R.id.txtContact)).setText(Html.fromHtml(contact, Html.FROM_HTML_MODE_LEGACY));
        }
    }

    @Override
    public void systemError(String paramString, Throwable paramThrowable) {
        if(paramThrowable != null) {
            Log.e(paramString, paramThrowable);
            runOnUiThread(() -> {

                StringWriter stringWriter = new StringWriter();
                if(paramThrowable!=null) {
                    paramThrowable.printStackTrace(new PrintWriter(stringWriter));
                }
                else {
                    stringWriter.write("FAILED");
                }

                Intent intent = new Intent(this, BlueErrorActivity.class);
                intent.putExtra(BlueErrorActivity.ERROR_MESSAGE, paramThrowable.getMessage());
                intent.putExtra(BlueErrorActivity.ERROR_DETAIL, stringWriter.toString());
                intent.putExtra(BlueErrorActivity.TINT_COLOR, tintColor);
                intent.putExtra(BlueErrorActivity.LOGO_URL, logoUrl);
                intent.putExtra(BlueErrorActivity.CONTACT, contact);
                startActivity(intent);
                finish();
            });
        }
    }

    private void startTransaction(){
        removeTimer();
        Intent intent = new Intent(this, BlueTransactionActivity.class);
        startActivity(intent);
        finish();
    }

    private void startReceipt(){
        removeTimer();
        Intent intent = new Intent(this, BlueReceiptActivity.class);
        startActivity(intent);
        finish();
    }

    @Override
    public void showStandbyScreen(){
        removeTimer();
        Intent standbyIntent = new Intent(this, BlueStandbyActivity.class);
        startActivity(standbyIntent);
        finish();
    }
}