package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.blue.components.BlueButton1;

public class BlueVehicleFragment extends Fragment {

    private final int tintColor;
    private OnNextCLickListener listener;

    public BlueVehicleFragment(int tintColor, OnNextCLickListener listener) {
        this.tintColor = tintColor;
        this.listener = listener;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_vehicle, container, false);

        ((TextView)view.findViewById(R.id.txtTitle)).setTextColor(tintColor);
        BlueKeyboardFragment fragment = (BlueKeyboardFragment)getChildFragmentManager()
                .findFragmentById(R.id.fragment_keyboard);
        fragment.setTintColor(tintColor);
        fragment.setEmail(false);
        fragment.setMaxLength(10);

        BlueButton1 btnNext = view.findViewById(R.id.btnNext);
        btnNext.setBackgroundColor(tintColor);
        btnNext.setOnClickListener(v -> {
            if(listener != null){
                listener.onNextClick(fragment.getValue());
            }
        });

        return view;
    }

    public interface OnNextCLickListener {
        void onNextClick(String value);
    }
}