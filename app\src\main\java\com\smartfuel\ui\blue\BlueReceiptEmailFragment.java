package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.blue.components.BlueButton1;

public class BlueReceiptEmailFragment extends Fragment {

    private final int tintColor;
    private OnNextCLickListener listener;

    public BlueReceiptEmailFragment(int tintColor, OnNextCLickListener listener) {
        this.tintColor = tintColor;
        this.listener = listener;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_receipt_email, container, false);

        ((TextView)view.findViewById(R.id.txtTitle)).setTextColor(tintColor);

        BlueKeyboardFragment fragment = (BlueKeyboardFragment)getChildFragmentManager()
                .findFragmentById(R.id.fragment_keyboard);
        fragment.setTintColor(tintColor);
        fragment.setEmail(true);
        fragment.setMaxLength(255);

        BlueButton1 btnNext = view.findViewById(R.id.btnNext);
        btnNext.setBackgroundColor(tintColor);
        btnNext.setOnClickListener(v -> {
            if(listener != null){
                listener.onNextClick(fragment.getValue());
            }
        });

        return view;
    }

    public interface OnNextCLickListener {
        void onNextClick(String value);
    }
}