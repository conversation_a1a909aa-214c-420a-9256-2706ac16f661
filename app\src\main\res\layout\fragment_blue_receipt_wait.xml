<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal">

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_bolditalic"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title1"
        android:textColor="@color/Black"
        android:text="@string/receipt"
        android:layout_marginBottom="@dimen/margin_bottom1"/>

    <androidx.cardview.widget.CardView
        android:id="@+id/cardInfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/card_radius"
        app:cardElevation="0dp"
        app:contentPadding="@dimen/padding_button6"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin4"
        app:cardBackgroundColor="@color/Black">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/gibson_regular"
            android:gravity="center_horizontal"
            android:textSize="@dimen/text_size2"
            android:textColor="@color/White"
            android:text="@string/receipt_info" />

    </androidx.cardview.widget.CardView>

</LinearLayout>