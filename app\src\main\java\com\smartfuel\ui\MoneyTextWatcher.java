package com.smartfuel.ui;

import android.text.Editable;
import android.text.TextWatcher;
import android.widget.EditText;
import android.widget.TextView;

import java.lang.ref.WeakReference;
import java.math.BigDecimal;
import java.text.NumberFormat;

public class MoneyTextWatcher implements TextWatcher {
    WeakReference<EditText> editTextWeakReference;
    WeakReference<TextView> textViewWeakReference;

    public MoneyTextWatcher(EditText paramEditText) {
        this.editTextWeakReference = new WeakReference<>(paramEditText);
    }

    public MoneyTextWatcher(TextView paramTextView) {
        this.textViewWeakReference = new WeakReference<>(paramTextView);
    }

    public void afterTextChanged(Editable paramEditable) {
        EditText editText = this.editTextWeakReference != null ? this.editTextWeakReference.get() : null;
        TextView textView = this.textViewWeakReference != null ? this.textViewWeakReference.get() : null;
        if (editText == null && textView == null)
            return;
        String str2 = paramEditable.toString();
        if (str2.isEmpty())
            return;
        if(editText != null)
            editText.removeTextChangedListener(this);
        if(textView != null)
            textView.removeTextChangedListener(this);
        //BigDecimal bigDecimal = (new BigDecimal(str2.replaceAll("[$,.]", ""))).setScale(2, 3).divide(new BigDecimal(100), 3);
        BigDecimal bigDecimal = (new BigDecimal(str2.replaceAll("[^\\d]", ""))).setScale(2).divide(new BigDecimal(1000), 3);
        String str1 = NumberFormat.getCurrencyInstance().format(bigDecimal);
        if(editText != null) {
            editText.setText(str1);
            editText.setSelection(str1.length());
            editText.addTextChangedListener(this);
        }
        if(textView != null) {
            textView.setText(str1);
            textView.addTextChangedListener(this);
        }
    }

    public void beforeTextChanged(CharSequence paramCharSequence, int paramInt1, int paramInt2, int paramInt3) {}

    public void onTextChanged(CharSequence paramCharSequence, int paramInt1, int paramInt2, int paramInt3) {}
}

