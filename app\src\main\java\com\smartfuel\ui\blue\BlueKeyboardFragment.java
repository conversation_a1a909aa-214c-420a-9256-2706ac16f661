package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.blue.components.BlueButton3;
import com.smartfuel.ui.utils.ViewUtils;

import java.util.List;

public class BlueKeyboardFragment extends Fragment implements View.OnClickListener {

    private TextView txtInput;
    private View layEmail;
    private int maxLength = 250;

    private List<BlueButton3> buttons;
    private boolean upper = true;
    private String value = "";

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_keyboard, container, false);

        txtInput = view.findViewById(R.id.txtInput);
        layEmail = view.findViewById(R.id.layEmail);

        if(!value.isEmpty())
            txtInput.setText(value);

        buttons = ViewUtils.findViewsWithType(view, BlueButton3.class);

        for (BlueButton3 btn : buttons) {
            btn.setOnClickListener(this);
        }
        return view;
    }

    @Override
    public void onClick(View v) {
        String currentValue = txtInput.getText().toString();
        String nextValue = "";
        switch (v.getTag().toString()) {
            case "Backspace":
                if(currentValue.length() > 0)
                    nextValue = currentValue.substring(0, currentValue.length()-1);
                break;
            case "Aa":
                nextValue = currentValue;
                upper = !upper;
                changeUpper();
                break;
            default:
                String tag = v.getTag().toString();
                nextValue = currentValue + tag;
                if(nextValue.length() > maxLength)
                    nextValue = nextValue.substring(0, maxLength);
                break;
        }
        txtInput.setText(nextValue);
    }

    public void setTintColor(int tintColor){
        txtInput.setBackgroundColor(tintColor);
    }

    public void setMaxLength(int maxLength){
        this.maxLength = maxLength;
    }

    public void setEmail(boolean isEmail){
        if(!isEmail)
            layEmail.setVisibility(View.GONE);
        else {
            layEmail.setVisibility(View.VISIBLE);
            upper = false;
            changeUpper();
        }
    }

    public String getValue(){
        value = txtInput.getText().toString();
        return value;
    }

    private void changeUpper(){
        for (BlueButton3 btn : buttons) {
            String text = btn.getText().toString();
            String tag = btn.getTag().toString();
            if(!tag.equals("Backspace") && !tag.equals("Aa")){
                if(upper){
                    text = text.toUpperCase();
                    tag = tag.toUpperCase();
                }
                else {
                    text = text.toLowerCase();
                    tag = tag.toLowerCase();
                }
                btn.setText(text);
                btn.setTag(tag);
            }
        }
    }
}