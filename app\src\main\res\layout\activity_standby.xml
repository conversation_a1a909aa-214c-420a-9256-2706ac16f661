<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:orientation="vertical"
    android:background="#ffffffff"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView
        android:id="@id/idIVLogo"
        android:layout_width="match_parent"
        android:layout_height="145.0dip"
        android:layout_marginTop="125.0dip"
        android:layout_marginBottom="125.0dip"
        android:src="@drawable/sflogo"
        android:layout_centerInParent="true"
        android:contentDescription="@string/smartfuel_logo"
        android:layout_marginStart="25.0dip"
        android:layout_marginEnd="25.0dip" />
    <TextView
        android:id="@+id/standbyText"
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:paddingLeft="10px"
        android:paddingRight="10px"
        android:fontFamily="@font/montserrat_semibold"
        android:textColor="@color/DarkGrey"
        android:textSize="70px"
        android:paddingTop="100dp"
        android:gravity="center"
        android:text="Pre-Pay here when the store is closed.">

    </TextView>
</LinearLayout>