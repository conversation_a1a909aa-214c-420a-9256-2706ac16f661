package com.smartfuel.ui.blue;

import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.utils.ImageDownloader;

public class BlueAppBarFragment extends Fragment {

    private View.OnClickListener backClickListener;
    private View.OnClickListener cancelClickListener;
    private View.OnClickListener promoClickListener;
    private View.OnClickListener doneClickListener;

    private View buttonBack;
    private View buttonCancel;
    private View buttonPromo;
    private View buttonDone;
    private View viewBackground;
    private ImageView logo;

    private ImageDownloader imageDownloader;

    public void setBackClickListener(View.OnClickListener listener) {
        this.backClickListener = listener;
    }

    public void setCancelClickListener(View.OnClickListener listener) {
        this.cancelClickListener = listener;
    }

    public void setPromoClickListener(View.OnClickListener listener) {
        this.promoClickListener = listener;
    }

    public void setDoneClickListener(View.OnClickListener listener) {
        this.doneClickListener = listener;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        imageDownloader = new ImageDownloader(getContext());

        View view = inflater.inflate(R.layout.fragment_blue_app_bar, container, false);

        viewBackground = view.findViewById(R.id.viewBackground);
        buttonBack = view.findViewById(R.id.button_back);
        buttonBack.setOnClickListener(v -> {
            if (backClickListener != null) {
                backClickListener.onClick(v);
            }
        });

        buttonCancel = view.findViewById(R.id.button_cancel);
        buttonCancel.setOnClickListener(v -> {
            if (cancelClickListener != null) {
                cancelClickListener.onClick(v);
            }
        });

        buttonPromo = view.findViewById(R.id.button_promo);
        buttonPromo.setOnClickListener(v -> {
            if (promoClickListener != null) {
                promoClickListener.onClick(v);
            }
        });

        buttonDone = view.findViewById(R.id.button_done);
        buttonDone.setOnClickListener(v -> {
            if (doneClickListener != null) {
                doneClickListener.onClick(v);
            }
        });

        logo = view.findViewById(R.id.logo);

        return view;
    }

    public void setTintColor(String color){
        int clr = Color.parseColor(color);
        viewBackground.setBackgroundColor(clr);
        buttonBack.setBackgroundColor(clr);
        buttonCancel.setBackgroundColor(clr);
        buttonPromo.setBackgroundColor(clr);
        buttonDone.setBackgroundColor(clr);
    }

    public void setLogo(String url){
        if(!url.isEmpty()) {
            imageDownloader.loadImage(url, bitmap -> {
                if (bitmap != null) {
                    logo.setImageBitmap(bitmap);
                }
            });
        }
    }

    public void showCancel(boolean show){
        if(show)
            buttonCancel.setVisibility(View.VISIBLE);
        else
            buttonCancel.setVisibility(View.GONE);
    }

    public void showBack(boolean show){
        if(show)
            buttonBack.setVisibility(View.VISIBLE);
        else
            buttonBack.setVisibility(View.GONE);
    }

    public void showPromo(boolean show){
        if(show)
            buttonPromo.setVisibility(View.VISIBLE);
        else
            buttonPromo.setVisibility(View.GONE);
    }

    public void showDone(boolean show){
        if(show)
            buttonDone.setVisibility(View.VISIBLE);
        else
            buttonDone.setVisibility(View.GONE);
    }
}
