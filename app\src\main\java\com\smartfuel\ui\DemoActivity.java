package com.smartfuel.ui;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.smartfuel.R;

public class DemoActivity extends AppCompatActivity {
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_demo);
        
        TextView statusText = findViewById(R.id.demo_status_text);
        Button testButton = findViewById(R.id.demo_test_button);
        
        statusText.setText("SmartFuel Kiosk - Demo Mode\n\nService is running in offline mode for testing purposes.");
        
        testButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                statusText.setText("Demo button clicked!\n\nThis confirms the app is working in demo mode.");
            }
        });
    }
}
