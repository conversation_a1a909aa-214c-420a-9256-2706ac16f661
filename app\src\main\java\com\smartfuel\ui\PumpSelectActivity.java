package com.smartfuel.ui;

import android.os.Bundle;

import android.content.ComponentName;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Handler;
import android.os.IBinder;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;

//import com.smarteist.autoimageslider.IndicatorView.animation.type.IndicatorAnimationType;
//import com.smarteist.autoimageslider.SliderView;

import com.smartfuel.R;
import com.smartfuel.service.models.forecourt.FuelPoint;
import com.smartfuel.service.models.forecourt.GradePrice;
import com.smartfuel.service.OPTService;


import org.w3c.dom.Text;

import java.util.ArrayList;
import java.util.List;

public class PumpSelectActivity extends BaseActivity implements FuelPointViewAdapter.ItemClickListener {
    BottomSheetDialog dialog = null;

    FuelGradePriceViewAdapter fuelGradePriceViewAdapter;

    FuelPointViewAdapter fuelPointViewAdapter;

    ArrayList<FuelPoint> fuelPoints;

    ArrayList<GradePrice> gradePrices;

    //int[] images = new int[] { R.drawable.sf_banner };

    private ServiceConnection mConnection = new ServiceConnection() {
        public void onServiceConnected(ComponentName param1ComponentName, IBinder param1IBinder) {
            try{
                OPTService.LocalBinder localBinder = (OPTService.LocalBinder)param1IBinder;
                PumpSelectActivity.this.myOPTService = localBinder.getServiceInstance();

                PumpSelectActivity pumpSelectActivity = PumpSelectActivity.this;

                //forecourt refactor
                //pumpSelectActivity.gradePrices = pumpSelectActivity.myOPTService.pss5000Service.getConfiguredGradePrices().getGradePrices();
                pumpSelectActivity.gradePrices = pumpSelectActivity.myOPTService.getConfiguredGradePrices().getGradePrices();
                pumpSelectActivity = PumpSelectActivity.this;
                //forecourt refactor
                //pumpSelectActivity.fuelPoints = pumpSelectActivity.myOPTService.pss5000Service.getConfiguredFuelPoints();
                pumpSelectActivity.fuelPoints = pumpSelectActivity.myOPTService.getConfiguredFuelPoints();

                RecyclerView recyclerView = PumpSelectActivity.this.findViewById(R.id.FuelPoints);
                recyclerView.setLayoutManager(
                        new GridLayoutManager(
                                PumpSelectActivity.this.myPumpSelectActivity,
                                4)
                );

                PumpSelectActivity.this.fuelPointViewAdapter =
                        new FuelPointViewAdapter(
                                PumpSelectActivity.this.myPumpSelectActivity,
                                PumpSelectActivity.this.fuelPoints);
                PumpSelectActivity.this.fuelPointViewAdapter.setClickListener(
                        PumpSelectActivity.this.myPumpSelectActivity);

                recyclerView.setAdapter(PumpSelectActivity.this.fuelPointViewAdapter);
            }
            catch (Exception e){
                myOPTService.Error(e);
            }

        }

        public void onServiceDisconnected(ComponentName param1ComponentName) {}
    };

    protected Handler myHandler;

    OPTService myOPTService;

    PumpSelectActivity myPumpSelectActivity = this;

    protected Runnable myRunnable;

    //SliderView sliderView;

    EditText trnAmountText;

    private void createDialog(String pumpNo, List<FuelPoint> fpData, List<GradePrice> fpGradeData) {
        final BottomSheetDialog dialog = new BottomSheetDialog(PumpSelectActivity.this, R.style.BottomSheetDialogTheme);
        dialog.setContentView(R.layout.activity_transaction_amount);
        dialog.getBehavior().setPeekHeight(1880);
        myHandler.removeCallbacks(myRunnable);
        myHandler.postDelayed(myRunnable, 15000L);
        dialog.show();
        trnAmountText = dialog.findViewById(R.id.editTextAmount);
        trnAmountText.addTextChangedListener(new MoneyTextWatcher(trnAmountText));
        Button btn10Increase = dialog.findViewById(R.id.increase_10);
        Button btn10Decrease = dialog.findViewById(R.id.decrease_10);
        Button btn20Increase = dialog.findViewById(R.id.increase_20);
        Button btn20Decrease = dialog.findViewById(R.id.decrease_20);
        Button btn50Increase = dialog.findViewById(R.id.increase_50);
        Button btn50Decrease = dialog.findViewById(R.id.decrease_50);
        Button btn100Increase = dialog.findViewById(R.id.increase_100);
        Button btn100Decrease = dialog.findViewById(R.id.decrease_100);
        Button btn200Increase = dialog.findViewById(R.id.increase_200);
        Button btn200Decrease = dialog.findViewById(R.id.decrease_200);
        Button btnDone = dialog.findViewById(R.id.btnDone);
        Button clearBtn = dialog.findViewById(R.id.btnClear);
        Button cancelBtn = dialog.findViewById(R.id.btnCancel);
        TextView pumpNumber = dialog.findViewById(R.id.pumpNumber);

        RecyclerView fuelRecyclerView = dialog.findViewById(R.id.fuelPriceList);

        fuelRecyclerView.setLayoutManager(new GridLayoutManager(this, 3));

        FuelPoint f = fpData.stream().filter(fuelPoint -> fuelPoint.getId().equals(String.format("%02d", Integer.parseInt(pumpNo)))).findFirst().orElse(null);
        ArrayList<GradePrice> fuelGrades = f.getGradePrices();
        ArrayList<GradePrice> myPumpGrades = new ArrayList<>();
        // this is the list of available fuel grades at this pump - iterate this list and create a vector of grades to show
        for (GradePrice fg : fuelGrades){
            GradePrice fuelPointFuelGrade = fpGradeData.stream().filter(fuelGrade-> fg.getId().equals(fuelGrade.getId())).findFirst().orElse(null);
            myPumpGrades.add(fuelPointFuelGrade);
        }
        fuelGradePriceViewAdapter = new FuelGradePriceViewAdapter(this, myPumpGrades);
        fuelRecyclerView.setAdapter(fuelGradePriceViewAdapter);

        pumpNumber.setText(pumpNo);

        assert btn10Increase != null;
        btn10Increase.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                float currentAmount = trnAmountText.getText().toString().equals("") ? 0 : Float.valueOf(trnAmountText.getText().toString().replaceAll("[^\\d]", ""));
                trnAmountText.setText(String.valueOf(currentAmount + 1000));
            }
        });
        assert btn10Decrease != null;
        btn10Decrease.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                float currentAmount = trnAmountText.getText().toString().equals("") ? 0 : Float.valueOf(trnAmountText.getText().toString().replaceAll("[^\\d]", ""));
                trnAmountText.setText(currentAmount-1000 >=0 ? String.valueOf(currentAmount - 1000) : String.valueOf(0));


            }
        });

        assert btn20Increase != null;
        btn20Increase.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                float currentAmount = trnAmountText.getText().toString().equals("") ? 0 : Float.valueOf(trnAmountText.getText().toString().replaceAll("[^\\d]", ""));
                trnAmountText.setText(String.valueOf(currentAmount + 2000));
            }
        });
        assert btn20Decrease != null;
        btn20Decrease.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                float currentAmount = trnAmountText.getText().toString().equals("") ? 0 : Float.valueOf(trnAmountText.getText().toString().replaceAll("[^\\d]", ""));
                trnAmountText.setText(currentAmount-2000 >=0 ? String.valueOf(currentAmount - 2000) : String.valueOf(0));


            }
        });

        assert btn50Increase != null;
        btn50Increase.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                float currentAmount = trnAmountText.getText().toString().equals("") ? 0 : Float.valueOf(trnAmountText.getText().toString().replaceAll("[^\\d]", ""));
                trnAmountText.setText(String.valueOf(currentAmount + 5000));
            }
        });
        assert btn50Decrease != null;
        btn50Decrease.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                float currentAmount = trnAmountText.getText().toString().equals("") ? 0 : Float.valueOf(trnAmountText.getText().toString().replaceAll("[^\\d]", ""));
                trnAmountText.setText(currentAmount-5000 >=0 ? String.valueOf(currentAmount - 5000) : String.valueOf(0));


            }
        });

        assert btn100Increase != null;
        btn100Increase.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                float currentAmount = trnAmountText.getText().toString().equals("") ? 0 : Float.valueOf(trnAmountText.getText().toString().replaceAll("[^\\d]", ""));
                trnAmountText.setText(String.valueOf(currentAmount + 10000));
            }
        });
        assert btn100Decrease != null;
        btn100Decrease.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                float currentAmount = trnAmountText.getText().toString().equals("") ? 0 : Float.valueOf(trnAmountText.getText().toString().replaceAll("[^\\d]", ""));
                trnAmountText.setText(currentAmount-10000 >=0 ? String.valueOf(currentAmount - 10000) : String.valueOf(0));


            }
        });

        assert btn200Increase != null;
        btn200Increase.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                float currentAmount = trnAmountText.getText().toString().equals("") ? 0 : Float.valueOf(trnAmountText.getText().toString().replaceAll("[^\\d]", ""));
                trnAmountText.setText(String.valueOf(currentAmount + 20000));
            }
        });
        assert btn200Decrease != null;
        btn200Decrease.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                float currentAmount = trnAmountText.getText().toString().equals("") ? 0 : Float.valueOf(trnAmountText.getText().toString().replaceAll("[^\\d]", ""));
                trnAmountText.setText(currentAmount-20000 >=0 ? String.valueOf(currentAmount - 20000) : String.valueOf(0));


            }
        });

        btnDone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                if (!trnAmountText.getText().toString().equals("")) {
                    String amount = (trnAmountText.getText().toString()).replace("$", "").replace(",", "").trim();
                    if (Float.valueOf(amount) < 10.00) {
                        showAlertDialog();
                    } else {
                        myHandler.removeCallbacks(myRunnable);
                        Intent i = new Intent(PumpSelectActivity.this, PresentPaymentActivity.class);
                        // add the selected pump and Amount to the intent so it is available in present payment activity
                        //pumpNumber
                        //trnAmountText
                        i.putExtra("selectedPump", pumpNumber.getText());
                        i.putExtra("trnAmount", trnAmountText.getText().toString());
                        startActivity(i);
                        dialog.dismiss();
                        finish();
                    }
                } else {
                    showAlertDialog();
                }

            }
        });

        clearBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                trnAmountText.setText("");
            }
        });
        cancelBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                myHandler.removeCallbacks(myRunnable);
                myHandler.postDelayed(myRunnable, 15000L);
                dialog.cancel();
            }
        });

    }

    protected void onCreate(Bundle paramBundle) {
        super.onCreate(paramBundle);
        bindService(new Intent((Context)this, OPTService.class), this.mConnection, BIND_AUTO_CREATE);
        setContentView(R.layout.activity_pump_select);

        //this.sliderView = findViewById(R.id.imageSlider);
        //SliderAdapter sliderAdapter = new SliderAdapter(this.images);
        //this.sliderView.setSliderAdapter(sliderAdapter);
        //this.sliderView.setIndicatorAnimation(IndicatorAnimationType.WORM);

        Handler handler = new Handler();
        this.myHandler = handler;
        Runnable runnable = new Runnable() {
            public void run() {
                if (PumpSelectActivity.this.dialog != null)
                    PumpSelectActivity.this.dialog.dismiss();
                Intent intent = new Intent((Context)PumpSelectActivity.this, HomeActivity.class);
                PumpSelectActivity.this.startActivity(intent);
                PumpSelectActivity.this.finish();
            }
        };
        this.myRunnable = runnable;
        handler.postDelayed(runnable, 15000L);
    }

    public void onItemClick(View paramView, int paramInt) {
        createDialog(this.fuelPointViewAdapter.getItem(paramInt), this.fuelPoints, this.gradePrices);
    }

    public void showAlertDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder((Context)this);
        builder.setTitle("Invalid amount").setMessage("Minimum amount is $10.00").setCancelable(false).setPositiveButton("OK", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface param1DialogInterface, int param1Int) {}
        });
        builder.create().show();
    }
}
