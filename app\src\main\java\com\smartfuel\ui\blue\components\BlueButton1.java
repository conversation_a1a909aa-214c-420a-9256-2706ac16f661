package com.smartfuel.ui.blue.components;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class BlueButton1 extends BaseBlueButton {

    private int backgroundColor = Color.BLUE;
    private int borderColor = Color.WHITE;
    private int textColor = Color.WHITE;

    @Override
    protected int getButtonBackgroundColor() {
        return backgroundColor == 0 ? Color.BLUE : backgroundColor;
    }

    @Override
    protected int getButtonBorderColor() {
        return borderColor == 0 ? Color.WHITE : borderColor;
    }

    @Override
    protected int getButtonTextColor() {
        return textColor == 0 ? Color.WHITE : textColor;
    }

    public BlueButton1(@NonNull Context context) {
        super(context);
    }

    public BlueButton1(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public BlueButton1(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void setBackgroundColor(@ColorInt int color){
        super.setBackgroundColor(color);
        backgroundColor = color;
    }
}
