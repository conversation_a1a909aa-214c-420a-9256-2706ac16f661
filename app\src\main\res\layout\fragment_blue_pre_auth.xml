<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal">


    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_bolditalic"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title1"
        android:textColor="@color/Black"
        android:text="@string/pre_auth_payment"
        android:layout_marginBottom="@dimen/margin_bottom2"/>

    <TextView
        android:id="@+id/txtInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_regular"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title3"
        android:textColor="@color/blue_black_text"
        android:text="@string/pre_auth_info"
        android:layout_marginBottom="@dimen/margin_bottom2"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin4"/>

    <androidx.cardview.widget.CardView
        android:id="@+id/cardInfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/card_radius"
        app:cardElevation="0dp"
        app:contentPadding="@dimen/padding_button6"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin4"
        app:cardBackgroundColor="@color/Black"
        android:layout_marginBottom="@dimen/margin_bottom2">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/gibson_regular"
            android:gravity="center_horizontal"
            android:textSize="@dimen/text_size2"
            android:textColor="@color/White"
            android:text="@string/card_info" />

    </androidx.cardview.widget.CardView>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_regular"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_size1"
        android:textColor="@color/blue_black_text"
        android:text="@string/cards_accepted"
        android:layout_marginBottom="@dimen/activity_horizontal_half_margin2"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_gravity="center"
        android:gravity="center"
        android:layout_marginBottom="@dimen/margin_bottom2"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin5">

        <ImageView
            android:id="@+id/icWhitecard"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:adjustViewBounds="true"
            android:src="@drawable/smartfuelcard"/>

        <androidx.cardview.widget.CardView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:cardCornerRadius="@dimen/card_radius"
            app:cardElevation="1dp"
            app:contentPadding="@dimen/padding_button3"
            app:contentPaddingTop="@dimen/padding_button9"
            app:contentPaddingBottom="@dimen/padding_button9"
            android:layout_marginHorizontal="@dimen/activity_horizontal_half_margin2">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="@dimen/image_size2"
                android:adjustViewBounds="true"
                android:src="@drawable/visa"
                android:layout_gravity="center_horizontal"/>

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:cardCornerRadius="@dimen/card_radius"
            app:cardElevation="1dp"
            app:contentPadding="@dimen/padding_button3"
            app:contentPaddingTop="@dimen/padding_button9"
            app:contentPaddingBottom="@dimen/padding_button9"
            android:layout_marginHorizontal="@dimen/activity_horizontal_half_margin2">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="@dimen/image_size2"
                android:adjustViewBounds="true"
                android:src="@drawable/mastercard"
                android:layout_gravity="center_horizontal"/>

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/amex_icon"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:cardCornerRadius="@dimen/card_radius"
            app:cardElevation="1dp"
            app:contentPadding="@dimen/padding_button3"
            app:contentPaddingTop="@dimen/padding_button9"
            app:contentPaddingBottom="@dimen/padding_button9"
            android:layout_marginHorizontal="@dimen/activity_horizontal_half_margin2">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="@dimen/image_size2"
                android:adjustViewBounds="true"
                android:src="@drawable/amex"
                android:layout_gravity="center_horizontal"/>

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/eftpos_icon"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:cardCornerRadius="@dimen/card_radius"
            app:cardElevation="1dp"
            app:contentPadding="@dimen/padding_button3"
            app:contentPaddingTop="@dimen/padding_button9"
            app:contentPaddingBottom="@dimen/padding_button9"
            android:layout_marginHorizontal="@dimen/activity_horizontal_half_margin2">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="@dimen/image_size2"
                android:adjustViewBounds="true"
                android:src="@drawable/eftpos"
                android:layout_gravity="center_horizontal"/>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

    <TextView
        android:id="@+id/txtInfoDiscount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_regular"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_size3"
        android:textColor="@color/blue_black_text"
        android:text="If you have a discount card, we will request this after the pre-auth payment has been approved."
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin1"/>

</LinearLayout>