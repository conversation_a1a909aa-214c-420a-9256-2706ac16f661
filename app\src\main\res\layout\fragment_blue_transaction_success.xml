<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal">

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_bolditalic"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title1"
        android:textColor="@color/Black"
        android:text="@string/time_to_fuel"
        android:layout_marginBottom="@dimen/margin_bottom1" />

    <androidx.cardview.widget.CardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/card_radius"
        app:cardElevation="0dp"
        app:contentPadding="@dimen/padding_button5"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin4"
        app:cardBackgroundColor="@color/blue_green"
        android:layout_marginBottom="@dimen/margin_bottom1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txtPumpNo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/gibson_bold"
                android:gravity="center_horizontal"
                android:textSize="@dimen/text_title2"
                android:textColor="@color/White"
                android:text="@string/proceed_to_pump"
                android:layout_marginBottom="@dimen/margin_bottom3"/>

            <TextView
                android:id="@+id/txtAmount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/gibson_regular"
                android:gravity="center_horizontal"
                android:textSize="@dimen/text_title3"
                android:textColor="@color/White"
                android:text="@string/amount_pre_authorised"
                android:layout_marginBottom="@dimen/margin_bottom3"/>

            <TextView
                android:id="@+id/txtTrxNumber"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/gibson_regular"
                android:gravity="center_horizontal"
                android:textSize="@dimen/text_title3"
                android:textColor="@color/White"
                android:text="@string/transaction_number" />

            <TextView
                android:id="@+id/txtDiscount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/gibson_regular"
                android:gravity="center_horizontal"
                android:textSize="@dimen/text_title3"
                android:textColor="@color/White"
                android:text="@string/discount_applied"
                android:layout_marginTop="@dimen/margin_bottom2"
                android:visibility="gone"/>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/txtAuthorizedGrades"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_regular"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title3"
        android:textColor="@color/blue_black_text"
        android:text="@string/authorized_info"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin6"
        android:visibility="gone"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/fuelGrades"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin6"
        android:layout_marginBottom="@dimen/margin_bottom1"
        android:visibility="gone"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_regular"
        android:gravity="left"
        android:textSize="@dimen/text_size1"
        android:textColor="@color/blue_black_text"
        android:text="@string/fuel_up_info"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin6"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"/>

</LinearLayout>