package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.os.Handler;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.blue.components.BlueButton1;
import com.smartfuel.ui.utils.ImageDownloader;

public class BlueTransactionDeclinedFragment extends Fragment {

    private final int tintColor;
    private final String logoURL;
    private final String contact;

    private String bankResponse;

    private ImageDownloader imageDownloader;

    private final DeclinedListener listener;

    private Handler myHandler;
    private Runnable myRunnable;

    long timeOut = 10000L;

    public BlueTransactionDeclinedFragment(int tintColor, String logoURL, String contact, DeclinedListener listener) {
        this.tintColor = tintColor;
        this.logoURL = logoURL;
        this.contact = contact;
        this.listener = listener;
    }

    protected void removeTimer(){
        if(myHandler != null)
            myHandler.removeCallbacks(myRunnable);
    }

    protected void setupTimer(long timeOut, Runnable action){
        this.myHandler = new Handler();
        this.myRunnable = () -> {
            myHandler.removeCallbacks(null);
            action.run();
        };
        startTimer(timeOut);
    }

    protected void startTimer(long timeOut){
        removeTimer();
        if(myHandler != null)
            myHandler.postDelayed(myRunnable, timeOut);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_transaction_declined, container, false);

        imageDownloader = new ImageDownloader(getContext());

        BlueButton1 btnCancel = view.findViewById(R.id.btnCancel);
        btnCancel.setBackgroundColor(tintColor);
        btnCancel.setOnClickListener(v -> {
            removeTimer();
            listener.onCancelClick();
        });

        if(!logoURL.isEmpty()) {
            ImageView logo = view.findViewById(R.id.logo);
            imageDownloader.loadImage(logoURL, bitmap -> {
                if (bitmap != null) {
                    logo.setImageBitmap(bitmap);
                }
            });
        }

        ((TextView)view.findViewById(R.id.txtBankResponse)).setText(bankResponse);

        if(!contact.isEmpty()){
            view.findViewById(R.id.layContact).setVisibility(View.VISIBLE);
            ((TextView)view.findViewById(R.id.txtContact)).setText(Html.fromHtml(contact, Html.FROM_HTML_MODE_LEGACY));
        }

        setupTimer(timeOut, () -> listener.onTimeOut());

        return view;
    }

    public void setBankResponse(String bankResponse){
        this.bankResponse = bankResponse;
    }

    public interface DeclinedListener{
        void onTimeOut();
        void onCancelClick();
    }
}