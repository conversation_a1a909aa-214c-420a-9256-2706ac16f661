<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent">

    <TextView
        android:id="@id/receiptTextView"
        android:layout_width="fill_parent"
        android:layout_height="100.0dip"
        android:background="@color/White"
        android:fontFamily="@font/montserrat_semibold"
        android:gravity="center|top"
        android:text="Preparing Receipts"
        android:textColor="@color/DarkGrey"
        android:textSize="30.0dip"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>