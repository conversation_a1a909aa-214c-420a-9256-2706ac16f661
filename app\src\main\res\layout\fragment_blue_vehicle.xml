<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal">

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_bolditalic"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title1"
        android:textColor="@color/Black"
        android:text="@string/enter_vehicle_registration"
        android:layout_marginBottom="@dimen/margin_bottom1"/>


    <fragment
        android:id="@+id/fragment_keyboard"
        android:name="com.smartfuel.ui.blue.BlueKeyboardFragment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin2"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"/>

    <com.smartfuel.ui.blue.components.BlueButton1
        android:id="@+id/btnNext"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/next"
        android:textSize="@dimen/text_title2"
        android:paddingVertical="@dimen/padding_button2"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin1"
        android:layout_marginBottom="@dimen/margin_bottom1"/>

</LinearLayout>