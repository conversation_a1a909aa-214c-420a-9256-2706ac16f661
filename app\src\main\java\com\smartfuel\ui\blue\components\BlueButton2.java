package com.smartfuel.ui.blue.components;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class BlueButton2 extends BaseBlueButton {

    private int backgroundColor = Color.WHITE;
    private int borderColor = Color.WHITE;
    private int textColor = Color.WHITE;

    @Override
    protected int getButtonBackgroundColor() {
        return backgroundColor == 0 ? Color.WHITE : backgroundColor;
    }

    @Override
    protected int getButtonBorderColor() {
        return borderColor == 0 ? Color.WHITE : borderColor;
    }

    @Override
    protected int getButtonTextColor() {
        return textColor == 0 ? Color.WHITE : textColor;
    }

    public BlueButton2(@NonNull Context context) {
        super(context);
    }

    public BlueButton2(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public BlueButton2(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setTintColor(@ColorInt int color){
        borderColor = color;
        setBorderColor(color);
        textColor = color;
        setTextColor(color);
    }
}
