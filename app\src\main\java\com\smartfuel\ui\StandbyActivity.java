package com.smartfuel.ui;

import android.annotation.SuppressLint;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowInsets;

import com.smartfuel.databinding.ActivityStandbyBinding;
import com.smartfuel.R;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.OPTService;
import com.smartfuel.service.logger.Log;
import com.smartfuel.service.models.forecourt.GradePrice;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;

/**
 * An example full-screen activity that shows and hides the system UI (i.e.
 * status bar and navigation/system bar) with user interaction.
 */
public class StandbyActivity extends BaseActivity implements IServiceEvents {
    private ServiceConnection mConnection = new ServiceConnection() {
        public void onServiceConnected(ComponentName componentName, IBinder binder) {
            OPTService.LocalBinder localBinder = (OPTService.LocalBinder) binder;
            StandbyActivity.this.myOPTService = localBinder.getServiceInstance();

            try {
                StandbyActivity.this.myOPTService.registerClient(StandbyActivity.this);
                StandbyActivity.this.setContentView(R.layout.activity_standby);
            } catch (Exception e) {
                myOPTService.Error(e);
            }
        }

        public void onServiceDisconnected(ComponentName componentName) {
            //startActivity(new Intent(StandbyActivity.this,StartupActivity.class));
            systemError("Error to create OPTService", new Exception("onServiceDisconnected"));
        }
    };

    OPTService myOPTService;


        @Override
        protected void onCreate(Bundle savedInstanceState) {
            super.onCreate(savedInstanceState);
            bindService(new Intent(this, OPTService.class), this.mConnection, BIND_AUTO_CREATE);
            Log.i("StandbyActivity", "Created");
        }

        @Override
        public void systemError(String paramString, Throwable paramThrowable) {
            Log.e("StartupActivity", "SystemError", paramThrowable);
            StringWriter stringWriter = new StringWriter();
            paramThrowable.printStackTrace(new PrintWriter(stringWriter));
            Intent intent = new Intent((Context) this, SystemErrorActivity.class);
            intent.putExtra("ErrorMessage", paramString);
            intent.putExtra("ErrorStackTrace", stringWriter.toString());
            startActivity(intent);
            finish();
        }

        @Override
        public void showHomeScreen() {
            Log.i("StandbyActivity", "Going Home");
            startActivity(new Intent((Context) this, HomeActivity.class));
            finish();
        }
    }