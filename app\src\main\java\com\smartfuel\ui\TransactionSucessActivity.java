package com.smartfuel.ui;

import android.os.Bundle;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Handler;
import android.os.IBinder;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.smartfuel.service.OPTService;
import com.smartfuel.R;
import com.smartfuel.service.models.forecourt.GradePrice;

import java.util.ArrayList;
import java.util.Arrays;

public class TransactionSucessActivity extends BaseActivity {
    private ServiceConnection mConnection = new ServiceConnection() {
        public void onServiceConnected(ComponentName componentName, IBinder binder) {
            OPTService.LocalBinder localBinder = (OPTService.LocalBinder)binder;
            TransactionSucessActivity.this.myOPTService = localBinder.getServiceInstance();
            showAuthorizedGrades();
        }

        public void onServiceDisconnected(ComponentName componentName) {}
    };

    OPTService myOPTService;

    protected void onCreate(Bundle paramBundle) {
        String str3;
        super.onCreate(paramBundle);
        setContentView(R.layout.activity_transaction_sucess);
        TextView transactionInfoView = findViewById(R.id.textTransactionInfo);
        TextView proceedPumpView = findViewById(R.id.textProceedtoPump);

        transactionInfoView.setText(String.format("Transaction #\n%s", new Object[] { getIntent().getStringExtra("trnId") }));
        proceedPumpView.setText(String.format("Proceed to pump %s for fuelling.", new Object[] { getIntent().getStringExtra("fuelPointId") }));

        bindService(new Intent(this, OPTService.class), this.mConnection, BIND_AUTO_CREATE);

        (new Handler()).postDelayed(new Runnable() {
            public void run() {
                Intent intent = new Intent((Context)TransactionSucessActivity.this, HomeActivity.class);
                TransactionSucessActivity.this.startActivity(intent);
                TransactionSucessActivity.this.finish();
            }
        },15000L);
    }

    private void showAuthorizedGrades() {
        runOnUiThread(() -> {
            String[] authorizedGrades = getIntent().getStringArrayExtra("authorizedGrades");
            if (authorizedGrades != null) {
                ArrayList<GradePrice> configuredGradePrices = null;
                ArrayList<GradePrice> authorizedGradePrices = new ArrayList<>();
                try {
                    //forecourt refactor
                    configuredGradePrices = TransactionSucessActivity.this.myOPTService.getConfiguredGradePrices().getGradePrices();

                    if (configuredGradePrices == null || configuredGradePrices.size() == 0) {
                        //throw new Exception("No Fuel Grades & Prices configured");
                    } else {
                        for (GradePrice gp : configuredGradePrices) {
                            if (Arrays.stream(authorizedGrades).anyMatch(gp.getId()::equals)) {
                                authorizedGradePrices.add(gp);
                            }
                        }
                        if (authorizedGradePrices.size() > 0) {
                            View txtAuthorizedGrades = findViewById(R.id.txtAuthorizedGrades);
                            txtAuthorizedGrades.setVisibility(View.VISIBLE);
                            RecyclerView recyclerView = findViewById(R.id.fuelPriceList);
                            recyclerView.setVisibility(View.VISIBLE);
                            recyclerView.setLayoutManager((RecyclerView.LayoutManager) new GridLayoutManager(getBaseContext(), 3));
                            FuelGradePriceViewAdapter fuelGradeListAdapter = new FuelGradePriceViewAdapter(getBaseContext(), authorizedGradePrices);
                            recyclerView.setAdapter(fuelGradeListAdapter);
                        }
                    }
                } catch (Exception e) {
                }
            }
        });
    }
}

