package com.smartfuel.ui;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.widget.TextView;

import com.smartfuel.BuildConfig;
import com.smartfuel.R;
import com.smartfuel.service.OPTService;

public class SystemErrorActivity extends BaseActivity {

    private static boolean isOpen = false;

    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        if(isOpen) {
            finish();
            return;
        }
        isOpen = true;
        setContentView(R.layout.activity_system_error);
        TextView textView = (TextView)findViewById(R.id.errorTextView);
        if (BuildConfig.DEBUG) {
            textView.setText(getIntent().getStringExtra("ErrorMessage") + "\n" + getIntent().getStringExtra("ErrorStackTrace"));
        } else {
            textView.setText(getIntent().getStringExtra("ErrorMessage"));
        }
        textView.setText(getIntent().getStringExtra("ErrorMessage"));
        // clean up the OPT Service in prearation for a re-connection.
        //disconnectFromService();
        //Attempt to restart the application by navigating to the startup activity
        (new Handler()).postDelayed(new Runnable() {
            public void run() {
                Intent intent = new Intent((Context)SystemErrorActivity.this, HomeActivity.class);
                SystemErrorActivity.this.startActivity(intent);
                SystemErrorActivity.isOpen = false;
                SystemErrorActivity.this.finish();
            }
        },15000L);
    }
}
