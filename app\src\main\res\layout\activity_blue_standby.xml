<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/card_radius"
        app:cardElevation="0dp"
        app:contentPadding="@dimen/padding_button5"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin4"
        app:cardBackgroundColor="@color/Black">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/logo"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/app_bar_logo"
                android:adjustViewBounds="true"
                android:src="@drawable/sflogo_dark"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/margin_bottom3"/>

            <TextView
                android:id="@+id/txtStandBy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/gibson_regular"
                android:gravity="center_horizontal"
                android:textSize="@dimen/text_title2"
                android:textColor="@color/White"
                android:text="Standby" />
        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>