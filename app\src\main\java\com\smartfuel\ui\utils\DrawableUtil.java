package com.smartfuel.ui.utils;

import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;

public class DrawableUtil {

    public static GradientDrawable createButtonBackground(int backgroundColor, int borderColor, float cornerRadius, int borderWidth) {
        GradientDrawable drawable = new GradientDrawable();
        drawable.setShape(GradientDrawable.RECTANGLE);
        drawable.setColor(backgroundColor);
        drawable.setStroke(borderWidth, borderColor);
        drawable.setCornerRadius(cornerRadius);
        return drawable;
    }

    public static int darkenColor(int color, float factor) {
        int a = Color.alpha(color);
        int r = Math.round(Color.red(color) * factor);
        int g = Math.round(Color.green(color) * factor);
        int b = Math.round(Color.blue(color) * factor);
        return Color.argb(a, Math.max(r, 0), Math.max(g, 0), Math.max(b, 0));
    }
}
