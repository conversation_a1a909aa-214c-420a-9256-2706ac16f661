package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.utils.ImageDownloader;

public class BluePreAuthFragment extends Fragment {

    private final int tintColor;
    private int amount;
    private final boolean showAmex;
    private final boolean showEftpos;
    private final boolean showWhiteCard;
    private final boolean showRewardCard;
    private final String rewardCardText;
    private final String whiteCardURL;

    private ImageDownloader imageDownloader;

    public BluePreAuthFragment(int tintColor, boolean showAmex, boolean showEftpos, boolean showWhiteCard, boolean showRewardCard, String rewardCardText, String whiteCardURL) {
        this.tintColor = tintColor;
        this.showAmex = showAmex;
        this.showEftpos = showEftpos;
        this.showWhiteCard = showWhiteCard;
        this.whiteCardURL = whiteCardURL;
        this.showRewardCard = showRewardCard;
        this.rewardCardText = rewardCardText;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_pre_auth, container, false);

        imageDownloader = new ImageDownloader(getContext());

        ((TextView)view.findViewById(R.id.txtTitle)).setTextColor(tintColor);
        TextView txtInfo = view.findViewById(R.id.txtInfo);
        String text = txtInfo.getText().toString();
        txtInfo.setText(Html.fromHtml(String.format("<b>$%d.00</b> %s", amount, text), Html.FROM_HTML_MODE_LEGACY));

        ((CardView)view.findViewById(R.id.cardInfo)).setCardBackgroundColor(tintColor);

        if(!showAmex)
            view.findViewById(R.id.amex_icon).setVisibility(View.GONE);

        if(!showEftpos)
            view.findViewById(R.id.eftpos_icon).setVisibility(View.GONE);

        ImageView icWhitecard = view.findViewById(R.id.icWhitecard);
        if(!showWhiteCard)
            icWhitecard.setVisibility(View.GONE);
        else if (!whiteCardURL.isEmpty())
            imageDownloader.loadImage(whiteCardURL, bitmap -> {
                if (bitmap != null) {
                    icWhitecard.setImageBitmap(bitmap);
                }
            });

        TextView txtInfoDiscount = view.findViewById(R.id.txtInfoDiscount);
        if(!showRewardCard)
            txtInfoDiscount.setVisibility(View.GONE);
        else{
            txtInfoDiscount.setText(String.format("If you have a %s, we will request this after the pre-auth payment has been approved.", rewardCardText));
        }

        return view;
    }

    public void setAmount(int amount){
        this.amount = amount;
    }
}