package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.smartfuel.R;
import com.smartfuel.ui.MoneyTextWatcher;

import java.math.BigDecimal;

public class BlueNumKeyboardFragment extends Fragment implements View.OnClickListener {

    private TextView txtInput;
    private int maxValue = 999999;
    private boolean isMoney;
    private int value = 0;

    private MoneyTextWatcher moneyTextWatcher;

    private ChangeValueListener listener;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_num_keyboard, container, false);

        txtInput = view.findViewById(R.id.txtInput);
        moneyTextWatcher = new MoneyTextWatcher(txtInput);
        if(txtInput.getText().toString().equals(""))
            txtInput.setText("0");
        if(value != 0)
            txtInput.setText(String.valueOf(value * (isMoney ? 1000 : 1)));

        int[] buttonIds = {R.id.btn0, R.id.btn1, R.id.btn2, R.id.btn3, R.id.btn4,
                R.id.btn5, R.id.btn6, R.id.btn7, R.id.btn8, R.id.btn9,
                R.id.btnBackspace};

        for (int id : buttonIds) {
            view.findViewById(id).setOnClickListener(this);
        }
        return view;
    }

    @Override
    public void onClick(View v) {
        int currentValue = getValue();
        int nextValue = 0;
        switch (v.getId()) {
            case R.id.btnBackspace:
                nextValue = (currentValue / 10);
                break;
            default:
                String digit = ((Button) v).getText().toString();
                int value = Integer.parseInt(digit);
                nextValue = (currentValue * 10) + value;
                if(nextValue > maxValue)
                    nextValue = currentValue;
                break;
        }
        if(isMoney)
            nextValue *= 1000;
        txtInput.setText(String.valueOf(nextValue));
        if(listener != null)
            listener.OnChangeValue(nextValue);
    }

    public void setTintColor(int tintColor){
        txtInput.setBackgroundColor(tintColor);
    }

    public void setMaxValue(int maxValue){
        this.maxValue = maxValue;
    }

    public void setMoney(boolean isMoney){
        this.isMoney = isMoney;
        if(isMoney)
            txtInput.addTextChangedListener(moneyTextWatcher);
        else
            txtInput.removeTextChangedListener(moneyTextWatcher);
        txtInput.setText(txtInput.getText());
    }

    public int getValue(){
        String currentValue = txtInput.getText().toString();
        if(isMoney){
            BigDecimal bigDecimal = (new BigDecimal(currentValue.replaceAll("[^\\d]", ""))).setScale(2).divide(new BigDecimal(100), 3);
            value = bigDecimal.toBigInteger().intValue();
        }
        else {
            value = Integer.parseInt(currentValue);
        }
        return value;
    }

    public void setOnChangeValueListener(ChangeValueListener listener){
        this.listener = listener;
    }

    public interface ChangeValueListener{
        void OnChangeValue(int value);
    }
}