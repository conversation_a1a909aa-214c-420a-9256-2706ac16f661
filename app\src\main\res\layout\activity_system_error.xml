<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="#ffffffff">
    <TextView
        android:id="@id/textErrorInfo"
        style="@style/Description.Bold"
        android:layout_width="900.0px"
        android:layout_height="450.0px"
        android:background="@drawable/rounded_corner"
        android:backgroundTint="@color/Red"
        android:gravity="center"
        android:padding="50.0px"
        android:text="@string/error"
        android:textColor="#ffffffff"
        app:cornerRadius="80.0px"
        android:layout_marginTop="80.0dip"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>


    <TextView
        android:id="@id/errorTextView"
        android:layout_width="fill_parent"
        android:layout_height="420.0dip"
        android:background="@color/White"
        android:fontFamily="@font/montserrat_semibold"
        android:gravity="center|top"
        android:text="System Error"
        android:textColor="@color/DarkGrey"
        android:textSize="20.0dip"
        android:padding="50.0px"
        android:layout_marginTop="250dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textErrorInfo" />

    <TextView
        android:id="@+id/supportNumber"
        android:text="@string/supportDetail"
        android:fontFamily="@font/montserrat_semibold"
        android:textColor="@color/DarkGrey"
        android:textSize="30.0px"
        android:layout_width="550px"
        android:layout_height="150px"
        android:layout_marginLeft="10dp"
        app:flow_horizontalAlign="start"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        />
</androidx.constraintlayout.widget.ConstraintLayout>