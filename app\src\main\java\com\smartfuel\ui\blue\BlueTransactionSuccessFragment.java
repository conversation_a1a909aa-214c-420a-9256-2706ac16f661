package com.smartfuel.ui.blue;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.smartfuel.R;
import com.smartfuel.service.models.forecourt.GradePrice;
import com.smartfuel.ui.blue.adapters.BlueFuelGradeViewAdapter;

import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

public class BlueTransactionSuccessFragment extends Fragment {

    private final int tintColor;

    private int pumpNo;
    private long amount;
    private long transactionNo;
    private Long litres;
    private String[] authorizedGrades;
    private List<GradePrice> configuredGradePrices;
    private String gradeColors;

    public BlueTransactionSuccessFragment(int tintColor, List<GradePrice> configuredGradePrices, String gradeColors) {
        this.tintColor = tintColor;
        this.configuredGradePrices = configuredGradePrices;
        this.gradeColors = gradeColors;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_transaction_success, container, false);

        ((TextView)view.findViewById(R.id.txtTitle)).setTextColor(tintColor);

        TextView txtPumpNo = view.findViewById(R.id.txtPumpNo);
        String text = txtPumpNo.getText().toString();
        txtPumpNo.setText(text + " " + pumpNo);

        DecimalFormatSymbols symbols = new DecimalFormatSymbols(Locale.US);
        symbols.setGroupingSeparator(',');
        symbols.setDecimalSeparator('.');
        symbols.setCurrencySymbol("$");
        DecimalFormat decimalFormat = new DecimalFormat("¤ #,##0.00", symbols);

        TextView txtAmount = view.findViewById(R.id.txtAmount);
        text = txtAmount.getText().toString();
        txtAmount.setText(decimalFormat.format(amount) + " " + text);

        TextView txtTrxNumber = view.findViewById(R.id.txtTrxNumber);
        text = txtTrxNumber.getText().toString();
        txtTrxNumber.setText(text + transactionNo);

        if(litres != null && litres != 0){
            TextView txtDiscount = view.findViewById(R.id.txtDiscount);
            txtDiscount.setVisibility(View.VISIBLE);
            String discounttxt = txtDiscount.getText().toString();
            txtDiscount.setText(discounttxt + String.format(" for up to %d litres.", litres.intValue()));
        }

        showAuthorizedGrades(view);

        return view;
    }

    public void setLitres(long litres){
        this.litres = litres;
    }

    public void setPumpNo(int pumpNo){
        this.pumpNo = pumpNo;
    }
    public void setAmount(long amount){
        this.amount = amount;
    }
    public void setTransactionNo(long transactionNo){
        this.transactionNo = transactionNo;
    }
    public void setAuthorizedGrades(String[] authorizedGrades){
        this.authorizedGrades = authorizedGrades;
    }

    private void showAuthorizedGrades(View view) {
        if (authorizedGrades != null && authorizedGrades.length > 0) {
            ArrayList<GradePrice> authorizedGradePrices = new ArrayList<>();
            try {
                if (configuredGradePrices == null || configuredGradePrices.size() == 0) {
                    //throw new Exception("No Fuel Grades & Prices configured");
                } else {
                    for (GradePrice gp : configuredGradePrices) {
                        if (Arrays.stream(authorizedGrades).anyMatch(gp.getId()::equals)) {
                            authorizedGradePrices.add(gp);
                        }
                    }
                    if (authorizedGradePrices.size() > 0) {
                        View txtAuthorizedGrades = view.findViewById(R.id.txtAuthorizedGrades);
                        txtAuthorizedGrades.setVisibility(View.VISIBLE);
                        RecyclerView recyclerView = view.findViewById(R.id.fuelGrades);
                        recyclerView.setVisibility(View.VISIBLE);
                        recyclerView.setLayoutManager((RecyclerView.LayoutManager) new GridLayoutManager(getContext(), authorizedGradePrices.size() < 3 ? authorizedGradePrices.size() : 3));
                        BlueFuelGradeViewAdapter fuelGradeListAdapter = new BlueFuelGradeViewAdapter(getContext(), authorizedGradePrices, gradeColors);
                        recyclerView.setAdapter(fuelGradeListAdapter);
                    }
                }
            } catch (Exception e) {
            }
        }
    }
}