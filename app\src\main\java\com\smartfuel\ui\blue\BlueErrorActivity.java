package com.smartfuel.ui.blue;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.smartfuel.BuildConfig;
import com.smartfuel.R;
import com.smartfuel.ui.BaseActivity;

public class BlueErrorActivity extends BaseActivity {

    private static boolean isOpen = false;

    public static final String ERROR_MESSAGE = "ERROR_MESSAGE";
    public static final String ERROR_DETAIL = "ERROR_DETAIL";
    public static final String TINT_COLOR = "TINT_COLOR";
    public static final String LOGO_URL = "LOGO_URL";
    public static final String CONTACT = "CONTACT";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if(isOpen) {
            finish();
            return;
        }
        isOpen = true;

        setContentView(R.layout.activity_blue_error);

        String errorMessage = getIntent().getStringExtra(ERROR_MESSAGE);
        String errorDetail = getIntent().getStringExtra(ERROR_DETAIL);
        String tintColor = getIntent().getStringExtra(TINT_COLOR);
        int tintColorInt = Color.parseColor(tintColor);
        String logoUrl = getIntent().getStringExtra(LOGO_URL);
        String contact = getIntent().getStringExtra(CONTACT);

        BlueErrorMessageFragment errorMessageFragment = new BlueErrorMessageFragment(tintColorInt, logoUrl, contact, new BlueErrorMessageFragment.OnErrorMessageListener() {
            @Override
            public void onTimeOut(Fragment currentFragment) {
                runOnUiThread(() -> {
                    backToHome();
                });
            }

            @Override
            public void onBackClick(Fragment currentFragment) {
                runOnUiThread(() ->{
                    backToHome();
                });
            }
        });

        errorMessageFragment.setErrorMessage(errorMessage);
        if (BuildConfig.DEBUG) {
            errorMessageFragment.setErrorDetail(errorDetail);
        }

        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        transaction.replace(R.id.fragment_container, errorMessageFragment);
        transaction.commit();
    }

    private void backToHome(){
        isOpen = false;
        Intent intent = new Intent(this, BlueHomeActivity.class);
        startActivity(intent);
        finish();
    }
}