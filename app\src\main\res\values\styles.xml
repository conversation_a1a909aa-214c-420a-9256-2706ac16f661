<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="SplashTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
    </style>


    <style name="Description.Bold" parent="ThemeOverlay.AppCompat">
        <item name="android:textSize">64px</item>
        <item name="android:fontFamily">@font/montserrat_semibold</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="Description.Subtitle" parent="ThemeOverlay.AppCompat">
        <item name="android:textSize">48px</item>
        <item name="android:fontFamily">@font/montserrat_semibold</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="BottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>

    </style>

    <style name="Button.Blue" parent="ThemeOverlay.AppCompat">
        <item name="android:backgroundTint">@color/LightBlue</item>
        <item name="android:textColor">@color/White</item>
        <item name="android:fontFamily">@font/montserrat_bold</item>
        <item name="cornerFamily">rounded</item>
    </style>

    <style name="Button.Green" parent="ThemeOverlay.AppCompat">
        <item name="android:backgroundTint">@color/Green</item>
        <item name="android:textSize">40px</item>
        <item name="android:fontFamily">@font/montserrat_bold</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50px</item>
    </style>

    <style name="Button.Red" parent="ThemeOverlay.AppCompat">
        <item name="android:backgroundTint">@color/Red</item>
        <item name="android:textSize">40px</item>
        <item name="android:fontFamily">@font/montserrat_bold</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50px</item>
    </style>

    <style name="Button.Small.Blue" parent="ThemeOverlay.AppCompat">
        <item name="android:backgroundTint">@color/DarkBlue</item>
        <item name="android:textSize">40px</item>
        <item name="android:fontFamily">@font/montserrat_bold</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="cornerFamily">rounded</item>
        <item name="cornerRadius">180px</item>
    </style>

    <style name="TextAppearance.Small.Gray" parent="ThemeOverlay.AppCompat">
        <item name="android:textSize">40px</item>
        <item name="android:fontFamily">@font/montserrat_bold</item>
        <item name="android:textColor">#727272</item>
        <item name="paddingStart">3pt</item>
        <item name="paddingEnd">3pt</item>
        <item name="android:paddingLeft">3pt</item>
        <item name="android:paddingRight">3pt</item>
    </style>


    <style name="Button.Gray" parent="ThemeOverlay.AppCompat">
        <item name="android:backgroundTint">#D8D8D8</item>
        <item name="android:textColor">#727272</item>
        <item name="android:fontFamily">@font/montserrat_bold</item>
        <item name="cornerFamily">rounded</item>
    </style>

    <style name="Widget.Theme.Kiosk.ActionBar.Fullscreen" parent="Widget.AppCompat.ActionBar">
        <item name="android:background">@color/black_overlay</item>
    </style>

    <style name="Widget.Theme.Kiosk.ButtonBar.Fullscreen" parent="">
        <item name="android:background">@color/black_overlay</item>
        <item name="android:buttonBarStyle">?android:attr/buttonBarStyle</item>
    </style>

    <style name="Widget.Theme.Kiosk.Button.Rounded" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

</resources>