<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

    <LinearLayout
        android:id="@+id/cardGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageView
            android:id="@id/bankCard"
            android:layout_width="400.0px"
            android:layout_height="400.0px"
            android:layout_marginTop="70.0dip"
            android:layout_weight="1.0"
            android:contentDescription="@string/qrImage"
            app:srcCompat="@drawable/bank_card" />

    </LinearLayout>

    <TextView
        android:id="@id/paymentInstruction"
        style="@style/Description.Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="300.0dip"
        android:text="@string/qrDescription"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cardGroup" />


    <LinearLayout
        android:id="@+id/navigationStrip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.91"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.96" >

        <Button
            android:id="@id/cancelBtn"
            style="@style/Button.Gray"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:text="@string/cancel"
            android:textSize="32.0px"
            app:cornerRadius="50.0px"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>