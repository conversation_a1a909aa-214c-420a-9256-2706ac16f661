package com.smartfuel.ui.blue;

import android.app.PendingIntent;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.hardware.usb.UsbManager;
import android.os.Bundle;
import android.os.IBinder;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.smartfuel.R;
import com.smartfuel.ReceiptsManager;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.connection.usb.UsbConnection;
import com.smartfuel.service.connection.usb.UsbPrinterConnection;
import com.smartfuel.service.escposprinter.EscPosPrinter;
import com.smartfuel.service.logger.Log;
import com.smartfuel.service.models.transaction.CardReceipt;
import com.smartfuel.service.models.transaction.WhiteCardReceipt;
import com.smartfuel.ui.BaseActivity;

import org.apache.commons.validator.routines.EmailValidator;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class BlueReceiptActivity extends BaseActivity implements IServiceEvents {

    private final String TAG = this.getClass().getSimpleName();

    private static final String ACTION_USB_PERMISSION = "com.smartfuel.USB_PERMISSION";

    BlueAppBarFragment appBarFragment;
    BlueStepIndicatorFragment stepsFragment;
    BlueReceiptHomeFragment receiptHomeFragment;
    BlueReceiptWaitFragment receiptWaitFragment;
    BlueReceiptDeclinedFragment receiptDeclinedFragment;
    BlueReceiptSuccessFragment receiptSuccessFragment;
    BlueReceiptEmailFragment receiptEmailFragment;
    BlueErrorMessageFragment errorMessageFragment;

    long timeOut = 65000L;
    boolean isPrint;

    private String cardType = "CARD";

    private String tintColor = "#1A588E";
    private String logoUrl = "";
    private String contact = "";

    protected List<String> steps = new ArrayList<>(Arrays.asList(
            "Select pump",
            "Enter $ amount",
            "Pre-auth payment",
            "Fuel up at selected pump",
            "Return here for a receipt"
    ));

    private void setupTimeOut(){
        setupTimer(timeOut, () -> {
            cancelReceipt(false);
        });
    }

    @Override
    public void onServiceConnected(ComponentName componentName, IBinder binder) {
        super.onServiceConnected(componentName, binder);
        try{
            getService().registerClient(this);
            initialize();
        }catch (Exception e){
            Log.e(TAG, e);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        connectToService(this);
    }

    private void initialize(){
        setContentView(R.layout.activity_blue_receipt);

        tintColor = getService().getConfiguration("tint_color", "#1A588E");
        int tintColorInt = Color.parseColor(tintColor);

        logoUrl = getService().getConfiguration("logo_url", "");

        appBarFragment = (BlueAppBarFragment) getSupportFragmentManager()
                .findFragmentById(R.id.fragment_custom_app_bar);

        appBarFragment.showCancel(false);

        if (appBarFragment != null) {
            appBarFragment.setBackClickListener(v -> {
                backTransaction();
            });
            appBarFragment.setCancelClickListener(v -> {
                cancelReceipt(true);
            });

            appBarFragment.setTintColor(tintColor);

            appBarFragment.setLogo(logoUrl);
        }

        stepsFragment = new BlueStepIndicatorFragment(steps, tintColorInt);
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        transaction.replace(R.id.fragment_steps, stepsFragment);

        getSupportFragmentManager().registerFragmentLifecycleCallbacks(new FragmentManager.FragmentLifecycleCallbacks() {
            @Override
            public void onFragmentViewCreated(@NonNull FragmentManager fm, @NonNull Fragment f, @NonNull View v, @Nullable Bundle savedInstanceState) {
                super.onFragmentViewCreated(fm, f, v, savedInstanceState);

                if (f instanceof BlueStepIndicatorFragment) {
                    stepsFragment.nextStep();
                    stepsFragment.nextStep();
                    stepsFragment.nextStep();
                    stepsFragment.nextStep();
                }
            }
        }, true);

        receiptHomeFragment = new BlueReceiptHomeFragment(tintColorInt, new BlueReceiptHomeFragment.OnClickListener() {
            @Override
            public void onPrintClick() {
                runOnUiThread(() ->{
                    isPrint = true;
                    receiptWaitFragment.setIsPrint(isPrint);
                    receiptDeclinedFragment.setIsPrint(isPrint);
                    receiptSuccessFragment.setIsPrint(isPrint);
                    moveTo(receiptWaitFragment, true);
                });
            }

            @Override
            public void onEmailClick() {
                runOnUiThread(() ->{
                    isPrint = false;
                    receiptWaitFragment.setIsPrint(isPrint);
                    receiptDeclinedFragment.setIsPrint(isPrint);
                    receiptSuccessFragment.setIsPrint(isPrint);
                    moveTo(receiptWaitFragment, true);
                });
            }
        });
        transaction.replace(R.id.fragment_container, receiptHomeFragment);
        transaction.commit();

        receiptWaitFragment = new BlueReceiptWaitFragment(tintColorInt);

        contact = getService().getConfiguration("contact_text", "");
        receiptDeclinedFragment = new BlueReceiptDeclinedFragment(tintColorInt, contact, new BlueReceiptDeclinedFragment.DeclinedListener() {
            @Override
            public void onTimeOut(boolean repeat) {
                if(repeat) {
                    runOnUiThread(() -> {
                        moveTo(receiptHomeFragment, false);
                    });
                }
                else
                    cancelReceipt(true);
            }

            @Override
            public void onCancelClick() {
                runOnUiThread(() -> {
                    moveTo(receiptHomeFragment, false);
                });
            }
        });

        receiptSuccessFragment = new BlueReceiptSuccessFragment(tintColorInt, ()->cancelReceipt(true));

        receiptEmailFragment = new BlueReceiptEmailFragment(tintColorInt, email -> {
            if(!email.isEmpty() && EmailValidator.getInstance()
                    .isValid(email)) {
                try {
                    getService().sendTransactionReceipt(null, email);
                    runOnUiThread(() -> {
                        moveTo(receiptSuccessFragment, true);
                    });
                } catch (Exception e) {
                    systemError(TAG, e);
                }
            }else{
                showMessageError("Invalid email");
            }
        });

        errorMessageFragment = new BlueErrorMessageFragment(tintColorInt, logoUrl, contact, new BlueErrorMessageFragment.OnErrorMessageListener() {
            @Override
            public void onTimeOut(Fragment currentFragment) {
                runOnUiThread(() -> {
                    moveTo(currentFragment, false);
                });
            }

            @Override
            public void onBackClick(Fragment currentFragment) {
                runOnUiThread(() ->{
                    moveTo(currentFragment, false);
                });
            }
        });

        setupTimeOut();
    }

    private void moveTo(Fragment fragment, boolean forward){
        startTimer(timeOut);
        FragmentManager fragmentManager = getSupportFragmentManager();

        Fragment currentFragment = fragmentManager.findFragmentById(R.id.fragment_container);

        try {
            if (fragment == receiptWaitFragment){
                getService().initialiseReceipt();
            }
            if (fragment == receiptHomeFragment || fragment == receiptWaitFragment){
                appBarFragment.showBack(true);
                appBarFragment.showCancel(false);
            }
            else if (fragment == receiptSuccessFragment) {
                appBarFragment.showBack(false);
                appBarFragment.showCancel(false);
            }
            else {
                appBarFragment.showBack(false);
                appBarFragment.showCancel(true);
            }

            if (currentFragment != null) {
                if(currentFragment == receiptWaitFragment && fragment == receiptHomeFragment){
                    try {
                        getService().cancelCardTransaction();
                    } catch (InterruptedException e) {
                        Log.e(TAG, e);
                    }
                }
                if(currentFragment == receiptDeclinedFragment && fragment == receiptDeclinedFragment){
                    return;
                }
                if(currentFragment == errorMessageFragment && fragment == errorMessageFragment){
                    return;
                }
                if(fragment == errorMessageFragment)
                    errorMessageFragment.setCurrentFragment(currentFragment);
            }

            FragmentTransaction transaction = fragmentManager.beginTransaction();
            int animEnter = 0;
            int animExit = 0;
            int animPopEnter = 0;
            int animPopExit = 0;
            if(fragment == errorMessageFragment){
                animEnter = R.anim.enter_from_bottom;
                animExit = R.anim.exit_to_top;
                animPopEnter = R.anim.enter_from_top;
                animPopExit = R.anim.exit_to_bottom;
            }
            else if(currentFragment == errorMessageFragment){
                animEnter = R.anim.enter_from_top;
                animExit = R.anim.exit_to_bottom;
                animPopEnter = R.anim.enter_from_bottom;
                animPopExit = R.anim.exit_to_top;
            }
            else if(forward) {
                animEnter = R.anim.slide_in_right;
                animExit = R.anim.slide_out_left;
                animPopEnter = R.anim.slide_in_right;
                animPopExit = R.anim.slide_out_left;
            }
            else {
                animEnter = R.anim.slide_in_left;
                animExit = R.anim.slide_out_right;
                animPopEnter = R.anim.slide_in_left;
                animPopExit = R.anim.slide_out_right;
            }
            transaction.setCustomAnimations(animEnter, animExit, animPopEnter, animPopExit);
            transaction.replace(R.id.fragment_container, fragment);
            transaction.addToBackStack(null);
            transaction.commitAllowingStateLoss();
        } catch (InterruptedException e) {
            systemError(TAG, e);
        }
    }

    private void cancelReceipt(boolean removeTimer){
        if(removeTimer)
            removeTimer();

        FragmentManager fragmentManager = getSupportFragmentManager();
        Fragment currentFragment = fragmentManager.findFragmentById(R.id.fragment_container);

        if (currentFragment != null) {
            if (currentFragment == receiptWaitFragment) {
                try {
                    getService().cancelCardTransaction();
                } catch (InterruptedException e) {
                    Log.e(TAG, e);
                }
            }
        }

        Intent intent = new Intent(this, BlueHomeActivity.class);
        this.startActivity(intent);
        this.finish();
    }

    private void backTransaction(){
        FragmentManager fragmentManager = getSupportFragmentManager();
        Fragment currentFragment = fragmentManager.findFragmentById(R.id.fragment_container);

        if (currentFragment != null) {
            if (currentFragment == receiptHomeFragment) {
                cancelReceipt(true);
            }
            else if (currentFragment == receiptWaitFragment) {
                moveTo(receiptHomeFragment, false);
            }
        }
    }

    @Override
    public void systemError(String paramString, Throwable paramThrowable) {
        if(!paramThrowable.getMessage().equals("Card Read Failed")) {
            if(paramThrowable != null) {
                Log.e(paramString, paramThrowable);
                runOnUiThread(() -> {

                    StringWriter stringWriter = new StringWriter();
                    if(paramThrowable!=null) {
                        paramThrowable.printStackTrace(new PrintWriter(stringWriter));
                    }
                    else {
                        stringWriter.write("FAILED");
                    }

                    Intent intent = new Intent(this, BlueErrorActivity.class);
                    intent.putExtra(BlueErrorActivity.ERROR_MESSAGE, paramThrowable.getMessage());
                    intent.putExtra(BlueErrorActivity.ERROR_DETAIL, stringWriter.toString());
                    intent.putExtra(BlueErrorActivity.TINT_COLOR, tintColor);
                    intent.putExtra(BlueErrorActivity.LOGO_URL, logoUrl);
                    intent.putExtra(BlueErrorActivity.CONTACT, contact);
                    startActivity(intent);
                    finish();
                });
            }
        }
    }

    @Override
    public void cardReceiptNotFound(){
        runOnUiThread(() -> {
            receiptDeclinedFragment.setError("We don’t recognise this payment card.");
            moveTo(receiptDeclinedFragment, true);
        });
    }

    @Override
    public void customerReceiptNotFount(){
        cardReceiptNotFound();
    }

    @Override
    public void cardReceiptDeclined(){
        runOnUiThread(() -> {
            receiptDeclinedFragment.setError("We don’t recognise this payment card.");
            moveTo(receiptDeclinedFragment, true);
        });
    }

    @Override
    public void cardReceiptCancelled(){

    }

    @Override
    public void cardReceiptTimeOut(){
        runOnUiThread(() -> {
            receiptDeclinedFragment.setError("Card Reader Timeout");
            receiptDeclinedFragment.setRepeat(false);
            moveTo(receiptDeclinedFragment, true);
        });
    }

    @Override
    public void cardReceiptComplete(List<Integer> terminalTransactionId){
        getService().PrepareCustomerReceipt(terminalTransactionId, cardType);
    }

    @Override
    public void customerReceiptDataReady(List<WhiteCardReceipt> whiteCardReceiptList) {
        runOnUiThread(() ->{
            if(isPrint) {
                if(printUsb(ReceiptsManager.getReceiptData(whiteCardReceiptList)))
                    moveTo(receiptSuccessFragment, true);
            }
            else{
                moveTo(receiptEmailFragment, true);
            }
        });
    }

    @Override
    public void customerCardReceiptDataReady(List<CardReceipt> cardReceipts) {
        runOnUiThread(() ->{
            if(isPrint) {
                if(printUsb(ReceiptsManager.getCardReceiptData(cardReceipts)))
                    moveTo(receiptSuccessFragment, true);
            }
            else{
                moveTo(receiptEmailFragment, true);
            }
        });
    }

    private boolean printUsb(List<String> receipts) {
        try {
            UsbConnection usbConnection = UsbPrinterConnection.selectElementUSBPrinter(this);
            UsbManager usbManager = (UsbManager) this.getSystemService(Context.USB_SERVICE);

            if (usbConnection == null || usbManager == null) {
                receiptDeclinedFragment.setError("Unable to print receipt.");
                moveTo(receiptDeclinedFragment, true);
                return false;
            }

            PendingIntent permissionIntent = PendingIntent.getBroadcast(
                    this.getApplicationContext(),
                    0,
                    new Intent(this.ACTION_USB_PERMISSION),
                    android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S ? PendingIntent.FLAG_MUTABLE : 0
            );

            usbManager.requestPermission(usbConnection.getDevice(), permissionIntent);

            EscPosPrinter printer = new EscPosPrinter(new UsbConnection(usbManager, usbConnection.getDevice()), 203, 80f, 45);
            for (String data : receipts) {
                printer.printFormattedTextAndCut(data, 500f);
            }
            return true;
        } catch (Exception e) {
            systemError(TAG, e);
        }
        return false;
    }

    public void showMessageError(String message) {
        errorMessageFragment.setErrorMessage(message);
        moveTo(errorMessageFragment, true);
    }
}