<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="20dp"
    android:background="#aaaaaa">


    <RelativeLayout
        android:id="@+id/btnBankCard"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="4"
        android:background="@drawable/border"
        android:layout_marginBottom="30dp">
        <TextView
            style="@style/Description.Bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Present Card at Terminal"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:textSize="20dp"/>
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            app:srcCompat="@drawable/bank_card"
            android:layout_centerInParent="true"/>
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/btnEmailReceipt"
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="30dp"
        android:layout_weight="1"
        android:background="@drawable/border"
        android:visibility="invisible"
        android:onClick="onEmailReceiptClick">

        <ImageView
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:layout_width="146dp"
            android:layout_height="match_parent"
            android:adjustViewBounds="false"
            app:srcCompat="@drawable/receipt_email_icon" />
        <TextView
            style="@style/Description.Bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Email Receipt"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:textSize="20dp"/>
    </LinearLayout>
    <LinearLayout
        android:id="@+id/btnPrintReceipt"
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="30dp"
        android:layout_weight="1"
        android:background="@drawable/border"
        android:visibility="invisible"
        android:onClick="onPrintReceiptClick"
        >

        <ImageView
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:layout_width="146dp"
            android:layout_height="match_parent"
            android:adjustViewBounds="false"
            app:srcCompat="@drawable/receipt_print_icon" />
        <TextView
            style="@style/Description.Bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Print Receipt"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:textSize="20dp"/>
    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="right">

        <Button
            android:id="@id/cancelBtn"
            style="@style/Button.Gray"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:text="@string/cancel"
            android:textSize="32.0px"
            app:cornerRadius="50.0px"
            android:onClick="onCancelClick"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>


    </LinearLayout>
</LinearLayout>