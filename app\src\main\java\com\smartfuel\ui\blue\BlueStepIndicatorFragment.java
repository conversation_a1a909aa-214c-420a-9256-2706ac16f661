package com.smartfuel.ui.blue;

import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.Fragment;

import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.LinearLayout;
import android.widget.TableLayout;
import android.widget.TableRow;
import android.widget.TextView;

import com.smartfuel.R;

import java.util.List;


public class BlueStepIndicatorFragment extends Fragment {

    private TableLayout stepsTable;
    private View stepsLine;
    private final int tintColor;
    private List<String> steps;
    private int stepNumber = 0;

    public BlueStepIndicatorFragment(List<String> steps, int tintColor){
        this.steps = steps;
        this.tintColor = tintColor;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_blue_step_indicator, container, false);
        stepsTable = view.findViewById(R.id.steps_table);
        stepsLine = view.findViewById(R.id.steps_line);

        setupSteps();

        nextStep();

        return view;
    }

    public void setupSteps(List<String> steps){
        this.steps = steps;
        stepsTable.removeAllViews();
        setupSteps();
        for(int i = 1; i <= stepNumber; i++){
            setStepStatus(i, true);
        }
    }

    private void setupSteps() {
        TableRow stepsRow = new TableRow(getContext());
        TableRow labelsRow = new TableRow(getContext());

        for (int i = 1; i <= steps.size(); i++) {
            // Create step circle
            LinearLayout circleLayout = new LinearLayout(getContext());
            circleLayout.setOrientation(LinearLayout.VERTICAL);
            circleLayout.setGravity(Gravity.CENTER_HORIZONTAL);
            TableRow.LayoutParams circleLayoutParams = new TableRow.LayoutParams(0, TableRow.LayoutParams.WRAP_CONTENT, 1f);
            circleLayout.setLayoutParams(circleLayoutParams);

            TextView circle = new TextView(getContext());
            LinearLayout.LayoutParams circleParams = new LinearLayout.LayoutParams(getContext().getResources().getDimensionPixelSize(R.dimen.indicator_size), getContext().getResources().getDimensionPixelSize(R.dimen.indicator_size));
            circle.setLayoutParams(circleParams);
            circle.setBackground(createCircleDrawable(ContextCompat.getColor(getContext(), R.color.blue_grey)));
            circle.setText(String.valueOf(i));
            circle.setTextSize(getContext().getResources().getDimension(R.dimen.indicator_num));
            circle.setTypeface(ResourcesCompat.getFont(getContext(), R.font.gibson_bolditalic));
            circle.setGravity(Gravity.CENTER);
            circle.setTextColor(ContextCompat.getColor(getContext(), android.R.color.white));
            circleLayout.addView(circle);
            stepsRow.addView(circleLayout);

            // Create step label
            TextView labelView = new TextView(getContext());
            TableRow.LayoutParams labelParams = new TableRow.LayoutParams(0, TableRow.LayoutParams.WRAP_CONTENT, 1f);
            labelParams.setMargins(0,getContext().getResources().getDimensionPixelSize(R.dimen.indicator_margin),0,0);
            labelView.setLayoutParams(labelParams);
            labelView.setText(steps.get(i-1));
            labelView.setTextSize(getContext().getResources().getDimension(R.dimen.indicator_text));
            labelView.setTextColor(ContextCompat.getColor(getContext(), R.color.blue_black_text));
            labelView.setTypeface(ResourcesCompat.getFont(getContext(), R.font.gibson_regular));
            labelView.setGravity(Gravity.CENTER_HORIZONTAL);
            labelsRow.addView(labelView);
        }

        stepsTable.addView(stepsRow);
        stepsTable.addView(labelsRow);

        adjustLineWidth();
    }

    private GradientDrawable createCircleDrawable(int color) {
        GradientDrawable drawable = new GradientDrawable();
        drawable.setShape(GradientDrawable.OVAL);
        drawable.setColor(color);
        drawable.setSize(70, 70);
        //drawable.setStroke(2, ContextCompat.getColor(getContext(), android.R.color.white)); // Optional: add border
        return drawable;
    }

    public void nextStep(){
        if(stepNumber + 1 <= steps.size()){
            stepNumber++;
            setStepStatus(stepNumber,true);
        }
    }

    public void previousStep(){
        if(stepNumber - 1 > 0){
            setStepStatus(stepNumber, false);
            stepNumber--;
        }
    }

    private void setStepStatus(int stepNumber, boolean isComplete) {
        if (stepNumber < 1 || stepNumber > steps.size()) {
            return;
        }
        TableRow stepsRow = (TableRow) stepsTable.getChildAt(0);
        TextView circle = (TextView) ((LinearLayout)stepsRow.getChildAt(stepNumber - 1)).getChildAt(0);
        int color = isComplete ? tintColor : ContextCompat.getColor(getContext(), R.color.blue_grey);
        circle.setBackground(createCircleDrawable(color));
    }

    private void adjustLineWidth() {
        ViewTreeObserver viewTreeObserver = stepsTable.getViewTreeObserver();
        viewTreeObserver.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                stepsTable.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                int childCount = stepsTable.getChildCount();
                if (childCount > 0) {
                    TableRow stepsRow = (TableRow) stepsTable.getChildAt(0);
                    if (stepsRow.getChildCount() > 1) {
                        View firstCircle = stepsRow.getChildAt(0);
                        View lastCircle = stepsRow.getChildAt(stepsRow.getChildCount() - 1);
                        int firstCircleCenterX = firstCircle.getLeft() + firstCircle.getWidth() / 2;
                        int lastCircleCenterX = lastCircle.getLeft() + lastCircle.getWidth() / 2;

                        ViewGroup.LayoutParams params = stepsLine.getLayoutParams();
                        params.width = lastCircleCenterX - firstCircleCenterX;
                        stepsLine.setLayoutParams(params);
                        stepsLine.setTranslationX(firstCircleCenterX);
                        stepsLine.setTranslationY(firstCircle.getTop() + firstCircle.getHeight() / 2 - stepsLine.getHeight() / 2);
                    }
                }
            }
        });
    }
}