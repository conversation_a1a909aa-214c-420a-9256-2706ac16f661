<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gibson_bolditalic"
        android:gravity="center_horizontal"
        android:textSize="@dimen/text_title1"
        android:textColor="@color/Black"
        android:layout_marginBottom="@dimen/margin_bottom1"/>


    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/card_radius"
        app:cardElevation="0dp"
        app:contentPadding="@dimen/padding_button5"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin4"
        app:cardBackgroundColor="@color/blue_red"
        android:layout_marginBottom="@dimen/margin_bottom1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/gibson_bold"
                android:gravity="center_horizontal"
                android:textSize="@dimen/text_title1"
                android:textColor="@color/White"
                android:text="@string/error"
                android:layout_marginBottom="@dimen/margin_bottom3"/>
            <TextView
                android:id="@+id/txtError"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/gibson_regular"
                android:gravity="center_horizontal"
                android:textSize="@dimen/text_title1"
                android:textColor="@color/White"
                android:text="@string/error"
                android:layout_marginBottom="@dimen/margin_bottom3"/>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/gibson_regular"
                android:gravity="center_horizontal"
                android:textSize="@dimen/text_title2"
                android:textColor="@color/White"
                android:text="@string/try_again" />
        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/imgleft"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:src="@drawable/seta"
            android:adjustViewBounds="true"
            android:layout_gravity="bottom"
            android:layout_marginBottom="@dimen/margin_bottom3"/>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <ImageView
                    android:id="@+id/imgQrCode"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:adjustViewBounds="true"
                    android:src="@drawable/qrcode"
                    android:maxHeight="@dimen/card_height1"/>

                <ImageView
                    android:id="@+id/imgCard"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:adjustViewBounds="true"
                    android:src="@drawable/rewardcard"
                    android:maxHeight="@dimen/card_height1"/>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"/>

            <com.smartfuel.ui.blue.components.BlueButton1
                android:id="@+id/btnSkip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/skip"
                android:textSize="@dimen/text_title2"
                android:paddingVertical="@dimen/padding_button2"
                android:layout_marginHorizontal="@dimen/activity_horizontal_margin2"
                android:layout_marginBottom="@dimen/margin_bottom1"/>

        </LinearLayout>

        <ImageView
            android:id="@+id/imgRight"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:src="@drawable/seta"
            android:adjustViewBounds="true"
            android:scaleX="-1"
            android:layout_gravity="bottom"
            android:layout_marginBottom="@dimen/margin_bottom3"/>

    </LinearLayout>

</LinearLayout>